/*
* @Author: <PERSON><PERSON>
* @Date:   2019-01-07 19:15:38
* @Last Modified by:   <PERSON><PERSON>
* @Last Modified time: 2019-01-07 19:15:38
*/

#ifndef __MYSQL_PARSER_COMMON_H__
#define __MYSQL_PARSER_COMMON_H__

#include <stdlib.h>
#include <inttypes.h>

// 返回字段值的最大长度
#define MAX_COL_VAL_LEN 1024

#define MYSQL_LOG_PRE   "[MySql]"

// Protocol::Handshake
#define MYSQL_PROTOCOL_VERSION_10                0x0a
#define MYSQL_PROTOCOL_VERSION_09                0x09

// response type
#define MYSQL_RESPONSE_PACKET_ERR                  -1
#define MYSQL_RESPONSE_PACKET_OK                    0
#define MYSQL_RESPONSE_PACKET_EOF                   1
#define MYSQL_RESPONSE_PACKET_DATA                  2

// parser status
#define PARSER_STATUS_CONTINUE                      0       // keep tcp stream offset
#define PARSER_STATUS_FINISH                        1       // set tcp stream offset
#define PARSER_STATUS_DROP_DATA                    -1       // drop tcp stream

#define CONTR_DIR(x) (STREAM_REQ ^ STREAM_RSP ^ (x))

//*****************************************************************************
#define MYSQL_STMT_HEADER 4
#define MYSQL_LONG_DATA_HEADER 6

// Capability Flags
#define CLIENT_LONG_PASSWORD                        0x00000001
#define CLIENT_FOUND_ROWS                           0x00000002
#define CLIENT_LONG_FLAG                            0x00000004 
#define CLIENT_CONNECT_WITH_DB                      0x00000008
#define CLIENT_NO_SCHEMA                            0x00000010
#define CLIENT_COMPRESS                             0x00000020
#define CLIENT_ODBC                                 0x00000040
#define CLIENT_LOCAL_FILES                          0x00000080
#define CLIENT_IGNORE_SPACE                         0x00000100
#define CLIENT_PROTOCOL_41                          0x00000200
#define CLIENT_INTERACTIVE                          0x00000400
#define CLIENT_SSL                                  0x00000800
#define CLIENT_IGNORE_SIGPIPE                       0x00001000
#define CLIENT_TRANSACTIONS                         0x00002000
#define CLIENT_RESERVED                             0x00004000
#define CLIENT_SECURE_CONNECTION                    0x00008000
#define CLIENT_MULTI_STATEMENTS                     0x00010000
#define CLIENT_MULTI_RESULTS                        0x00020000
#define CLIENT_PS_MULTI_RESULTS                     0x00040000
#define CLIENT_PLUGIN_AUTH                          0x00080000
#define CLIENT_CONNECT_ATTRS                        0x00100000
#define CLIENT_PLUGIN_AUTH_LENENC_CLIENT_DATA       0x00200000
#define CLIENT_CAN_HANDLE_EXPIRED_PASSWORDS         0x00400000
#define CLIENT_SESSION_TRACK                        0x00800000
#define CLIENT_DEPRECATE_EOF                        0x01000000

// Text Protocol Command
#define COM_SLEEP                                   0x00
#define COM_QUIT                                    0x01 // 01 00 00 00 01
#define COM_INIT_DB                                 0x02
#define COM_QUERY                                   0x03
#define COM_FIELD_LIST                              0x04
#define COM_CREATE_DB                               0x05
#define COM_DROP_DB                                 0x06
#define COM_REFRESH                                 0x07
#define COM_SHUTDOWN                                0x08
#define COM_STATISTICS                              0x09
#define COM_PROCESS_INFO                            0x0a // As of MySQL 5.7.11, COM_PROCESS_INFO is deprecated
#define COM_CONNECT                                 0x0b
#define COM_PROCESS_KILL                            0x0c
#define COM_DEBUG                                   0x0d
#define COM_PING                                    0x0e
#define COM_TIME                                    0x0f
#define COM_DELAYED_INSERT                          0x10
#define COM_CHANGE_USER                             0x11 // ???? need confirm
#define COM_BINLOG_DUMP                             0x12
#define COM_TABLE_DUMP                              0x13
#define COM_CONNECT_OUT                             0x14
#define COM_REGISTER_SLAVE                          0x15
#define COM_STMT_PREPARE                            0x16
#define COM_STMT_EXECUTE                            0x17
#define COM_STMT_SEND_LONG_DATA                     0x18
#define COM_STMT_CLOSE                              0x19
#define COM_STMT_RESET                              0x1a
#define COM_SET_OPTION                              0x1b
#define COM_STMT_FETCH                              0x1c
#define COM_DAEMON                                  0x1d
#define COM_BINLOG_DUMP_GTID                        0x1e
#define COM_RESET_CONNECTION                        0x1f

// Status Flags
#define SERVER_STATUS_IN_TRANS                      0x0001
#define SERVER_STATUS_AUTOCOMMIT                    0x0002
#define SERVER_MORE_RESULTS_EXISTS                  0x0008
#define SERVER_STATUS_NO_GOOD_INDEX_USED            0x0010
#define SERVER_STATUS_NO_INDEX_USED                 0x0020
#define SERVER_STATUS_CURSOR_EXISTS                 0x0040
#define SERVER_STATUS_LAST_ROW_SENT                 0x0080
#define SERVER_STATUS_DB_DROPPED                    0x0100
#define SERVER_STATUS_NO_BACKSLASH_ESCAPES          0x0200
#define SERVER_STATUS_METADATA_CHANGED              0x0400
#define SERVER_QUERY_WAS_SLOW                       0x0800
#define SERVER_PS_OUT_PARAMS                        0x1000
#define SERVER_STATUS_IN_TRANS_READONLY             0x2000
#define SERVER_SESSION_STATE_CHANGED                0x4000

// sub shutdown command
#define SHUTDOWN_DEFAULT                            0x00
#define SHUTDOWN_WAIT_CONNECTIONS                   0x01
#define SHUTDOWN_WAIT_TRANSACTIONS                  0x02
#define SHUTDOWN_WAIT_UPDATES                       0x08
#define SHUTDOWN_WAIT_ALL_BUFFERS                   0x10
#define SHUTDOWN_WAIT_CRITICAL_BUFFERS              0x11
#define KILL_QUERY                                  0xfe
#define KILL_CONNECTION                             0xff

// sub-systems to refresh
#define REFRESH_GRANT                               0x01
#define REFRESH_LOG                                 0x02
#define REFRESH_TABLES                              0x04
#define REFRESH_HOSTS                               0x08
#define REFRESH_STATUS                              0x10
#define REFRESH_THREADS                             0x20
#define REFRESH_SLAVE                               0x40
#define REFRESH_MASTER                              0x80

// column types
#define MYSQL_TYPE_DECIMAL                          0x00
#define MYSQL_TYPE_TINY                             0x01
#define MYSQL_TYPE_SHORT                            0x02
#define MYSQL_TYPE_LONG                             0x03
#define MYSQL_TYPE_FLOAT                            0x04
#define MYSQL_TYPE_DOUBLE                           0x05
#define MYSQL_TYPE_NULL                             0x06
#define MYSQL_TYPE_TIMESTAMP                        0x07
#define MYSQL_TYPE_LONGLONG                         0x08
#define MYSQL_TYPE_INT24                            0x09
#define MYSQL_TYPE_DATE                             0x0a
#define MYSQL_TYPE_TIME                             0x0b
#define MYSQL_TYPE_DATETIME                         0x0c
#define MYSQL_TYPE_YEAR                             0x0d
#define MYSQL_TYPE_NEWDATE                          0x0e
#define MYSQL_TYPE_VARCHAR                          0x0f
#define MYSQL_TYPE_BIT                              0x10
#define MYSQL_TYPE_TIMESTAMP2                       0x11
#define MYSQL_TYPE_DATETIME2                        0x12
#define MYSQL_TYPE_TIME2                            0x13
#define MYSQL_TYPE_NEWDECIMAL                       0xf6
#define MYSQL_TYPE_ENUM                             0xf7
#define MYSQL_TYPE_SET                              0xf8
#define MYSQL_TYPE_TINY_BLOB                        0xf9
#define MYSQL_TYPE_MEDIUM_BLOB                      0xfa
#define MYSQL_TYPE_LONG_BLOB                        0xfb
#define MYSQL_TYPE_BLOB                             0xfc
#define MYSQL_TYPE_VAR_STRING                       0xfd
#define MYSQL_TYPE_STRING                           0xfe
#define MYSQL_TYPE_GEOMETRY                         0xff

// Types of State Change Information
#define SESSION_TRACK_SYSTEM_VARIABLES              0x00
#define SESSION_TRACK_SCHEMA                        0x01
#define SESSION_TRACK_STATE_CHANGE                  0x02
#define SESSION_TRACK_GTIDS                         0x03
//*****************************************************************************

// mysql protocol connection status
typedef enum __proto_conn_status
{
    PROTO_CONN_INIT,
    PROTO_CONN_LOGIN,
    PROTO_CONN_REQUEST,
    PROTO_CONN_RESPONSE_OK,
    PROTO_CONN_RESPONSE_MESSAGE,
    PROTO_CONN_RESPONSE_TABULAR,
    PROTO_CONN_RESPONSE_SHOW_FIELDS,
    PROTO_CONN_FIELD_PACKET,
    PROTO_CONN_ROW_PACKET,
    PROTO_CONN_RESPONSE_PREPARE,
    PROTO_CONN_PREPARED_PARAMETERS,
    PROTO_CONN_PREPARED_FIELDS,
    PROTO_CONN_AUTH_SWITCH_REQUEST,
    PROTO_CONN_AUTH_SWITCH_RESPONSE 
} proto_conn_status;

typedef struct b_string
{
    const char *s;
    unsigned int len;
} b_string_t;

typedef struct mysql_status
{
    uint16_t charset;
    uint16_t stmt_num_params;
    uint16_t stmt_num_fields;
    uint32_t server_cap_flags;
    uint32_t client_cap_flags;
    uint32_t connection_id;
    proto_conn_status conn_stat;           // 服务端连接状态, S -> C
    uint16_t status_flags;
    int is_compress;
    b_string_t user;
    b_string_t password;
    b_string_t db_name;
    b_string_t server_version;
    int num_fields;
    uint8_t protocol_version;
    int is_ssl;
    int is_cmd_phase;

    char client_ip[64 + 1];
    int client_port;
    char server_ip[64 + 1];
    int server_port;
} mysql_status_t;

typedef struct mysql_row_data
{
    b_string_t **row;
} mysql_row_data_t;

typedef struct column_def
{
    uint8_t column_type;
    uint16_t charset;
    uint32_t col_max_len;
    int col_idx;
    b_string_t schema;
    b_string_t table;
    b_string_t column;
    b_string_t default_val;

    struct column_def *next;
} column_def_t;

typedef struct mysql_parsed_data
{
    char id[48];
    const b_string_t *user;                       // referenced to mysql_status_t
    const b_string_t *password;                   // referenced to mysql_status_t
    const b_string_t *db_name;                    // referenced to mysql_status_t
    const b_string_t *server_version;             // referenced to mysql_status_t
    b_string_t sql;
    int resource_type;
    int op_code;
    uint8_t sub_cmd;
    uint32_t stmt_id;
    uint32_t conn_id;
    uint32_t rows;                                // 影响行数

    column_def_t *col_def;
    int col_cnt;

    mysql_row_data_t **rs_rows;
    int row_cnt;

    int success;
    int err_code;
    char *err_msg;
    const char *client_ip;
    int client_port;
    const char *server_ip;
    int server_port;
    uint64_t tm;

    const char *c_mac;
    const char *s_mac;
    uint64_t req_size;
    uint64_t resp_size;
    uint64_t req_time;
} mysql_parsed_data_t;

typedef struct mysql_half_stream
{
    mysql_parsed_data_t data;
    struct mysql_half_stream *next;
} mysql_half_stream_t;

typedef struct mysql_stream
{
    mysql_half_stream_t *p_mysql_server;        // 服务器侧接收到的数据, call
    mysql_half_stream_t *p_mysql_client;        // 客户端侧接收到的数据, reply

    mysql_half_stream_t *p_mysql_server_last;   // 最早到达的call节点 匹配用
    mysql_half_stream_t *p_mysql_client_last;   // 最早到达的reply节点 匹配用

    mysql_status_t my_stat;

    int is_mysql;
} mysql_stream_t;

#endif /* __MYSQL_PARSER_COMMON_H__ */