/*
* @Author: <PERSON><PERSON>
* @Date:   2019-01-07 19:16:09
* @Last Modified by:   <PERSON><PERSON>
* @Last Modified time: 2019-01-08 16:42:30
*/

#include <stdio.h>
#include <stdlib.h>
#include <stdint.h>

#include "mysql_parser.h"
#include "mysql_parser_common.h"

#include "utils.h"

#include "gw_common.h"
#include "gw_logger.h"
#include "gw_config.h"
#include "session_mgt.h"
#include "session.h"

/**
 * 在接收数据时，探测数据流协议。
 * @param CSessionMgt *
 * @param app_stream_t *
 * @param struct conn *
 */
bool CMysqlParser::probe(CSessionMgt *psm, const app_stream_t *a_app, const struct conn *pcon)
{
    CSession *p_session;
    CSession *p_session_first;
    StreamData *p_sd;
    mysql_stream_t *p_ms;
    int dir = a_app->dir;
    bool is_mysql = false;

    mysql_check_show_info(p_session, pcon);

    p_session = p_session_first = psm->find_session(pcon);
    if (p_session == NULL)
    {
        p_session = psm->new_session(pcon);
        if (p_session == NULL)
        {
            return false;
        }
    }

    p_sd = get_stream_data_from_session(p_session, dir);
    if (NULL == p_sd || NULL == (p_ms = p_sd->p_mysql_stream))
    {
        return false;
    }

    // GWLOG_TEST(m_comm, "psd=%p\n", psd);

    // 先检查session中的标志
    if (p_ms->is_mysql == 1)
    {
        return true;
    }
    else if (p_ms->is_mysql == 2)
    {
        return false;
    }

    // 探测mysql协议   
    // 第一次处理tcp stream时检测下是否是mysql的thrift协议
    if (is_mysql_protocol(p_session, dir))
    {
        is_mysql = true;
        p_ms->is_mysql = 1;

        __sync_fetch_and_add(&m_stats_mysql.cnt_session_total, 1);
    }
    else
    {
        p_ms->is_mysql = 2;
    }

    if (p_session_first == NULL)
    {
        mysql_check_show_info(p_session, pcon);
    }

    return is_mysql;
}

/**
 * 在连接关闭时，探测数据流协议。
 * @param CSessionMgt *
 * @param app_stream_t *
 * @param struct conn *
 */
bool CMysqlParser::probe_on_close(CSessionMgt *, const app_stream_t *, const struct conn *)
{
    return false;
}

/**
 * 在连接重置时，探测数据流协议。
 * @param CSessionMgt *
 * @param app_stream_t *
 * @param struct conn *
 */
bool CMysqlParser::probe_on_reset(CSessionMgt *, const app_stream_t *, const struct conn *)
{
    return false;
}




