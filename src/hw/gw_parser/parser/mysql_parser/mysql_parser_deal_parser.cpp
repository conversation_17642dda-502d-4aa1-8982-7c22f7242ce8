/*
* @Author: <PERSON><PERSON>
* @Date:   2019-01-07 19:16:40
* @Last Modified by:   <PERSON><PERSON>
* @Last Modified time: 2019-01-24 14:48:47
*
* mysql数据包小于(2^24-1)字节，超过的会拆分，格式：
* +-------------------+----------------+-----------------------------
* | 3B payload length | 1B sequence id | <= 2^24-1 Bytes payload ...
* +-------------------+----------------+-----------------------------
*/

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <assert.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>

#include <uuid/uuid.h>
#include <zlib.h>

#include "gw_common.h"
#include "gw_logger.h"
#include "gw_config.h"
#include "session_mgt.h"
#include "session.h"
#include "gw_i_upload.h"

#include "worker_queue.h"
#include "gw_stats.h"
#include "cJSON.h"
#include "simple_json.h"

#include "mysql_parser.h"
#include "mysql_parser_common.h"

const int MAX_RESP_DATA_SIZE = 5 * 1024 * 1024;
const int MAX_RESP_ROW = 50;

static const char msg_event_type[] = "mysql_event";
static const char msg_content_type[] = "mysql_content";

/**
 * 在接收数据时，解析数据流协议。
 * @param CSessionMgt *
 * @param app_stream_t *
 * @param struct conn *
 */
int CMysqlParser::parse(CSessionMgt *psm, const app_stream_t *a_app, const struct conn *pcon)
{
    int i_ret = 0;
    const char *data = NULL;
    const char *uncomp_data = NULL;
    size_t uncomp_len = 0;
    size_t data_offset = 0, comp_data_offset = 0;
    int uncomp_pkt_len = 0;
    int data_len = 0;
    int dir = a_app->dir; // =0 call; =1 reply;
    int seqid = 0;
    size_t pkt_len = 0;
    int tcp_stream_offset = 0;
    CSession *p_session = psm->get_session(pcon);
    StreamData *p_sd = p_session->get_stream_data_from_parser(this);
    mysql_stream_t *p_stream = p_sd->p_mysql_stream;
    mysql_half_stream_t *p_hlf_stream = NULL;
    mysql_status_t *p_my_status = &p_stream->my_stat;
    mysql_parsed_data_t *p_mpd = NULL;
    bool parsed_data = false;
    bool free_local_parser = false;
    bool is_compressed = (p_my_status->client_cap_flags & CLIENT_COMPRESS) && (p_my_status->server_cap_flags & CLIENT_COMPRESS);

    data = p_session->get_data(this, dir, &data_len, &tcp_stream_offset);
    if (NULL == data || data_len < 4)
    {
        return 0;
    }
#ifdef _DEBUG
    print_mem(data, data_len);
#endif

    pkt_len = read_mysql_packet_length(data);
    seqid = (int)(*(data + 3));
    GWLOG_DEBUG(m_comm, "%s data length: %d, packet length: %llu, seqid: %d\n", MYSQL_LOG_PRE, data_len, pkt_len, seqid);
    if (is_compressed && p_my_status->is_cmd_phase/* command phase才会压缩数据 */)
    {
        if (pkt_len + 7 > data_len)
        {
            // 压缩数据长度不够
            p_session->discard(this, dir, 0);
            return 0;
        }

        comp_data_offset = pkt_len + 7;

        // 解压缩数据
        uncompress_mysql_packet((char **)&data, pkt_len, (char **)&uncomp_data, &uncomp_len);
        if (uncomp_data)
        {
            // 解压数据
            data = uncomp_data;
            // 一般，解压后数据长度应该等于compress header中指定的长度  
            data_len = uncomp_len;
        }
        else
        {
            data_len -= 7;      // 减去compress header 7B长度
        }

        // 解压缩后的mysql packet长度
        pkt_len = read_mysql_packet_length(data);
    }
    data_offset += 4;

    if ((pkt_len + data_offset) > data_len)
    {
        // 长度不够，等待下个数据包
        p_session->discard(this, dir, 0);
        if (uncomp_data)
        {
            free((void *)uncomp_data);
        }
        return 0;
    }

    proto_conn_status curr_conn_stat = p_my_status->conn_stat;

    if (p_my_status->client_port == 0 && p_my_status->server_port == 0)
    {
        set_ip_port(pcon, p_my_status);
    }

    // parser data
    p_hlf_stream = create_session_mysql_parser();
    p_mpd = &p_hlf_stream->data;

    p_mpd->tm = current_time_ms();

    do {
        if ((pkt_len + data_offset) > data_len)
        {
            // 数据不全，等待下一个数据包一起
            p_session->discard(this, dir, 0);
            free_local_parser = true;
            break;
        }

        int ret = parse_mysql_packet(data + data_offset, pkt_len, dir, p_my_status, p_mpd);
        if (PARSER_STATUS_DROP_DATA == ret)
        {
            p_session->tcp_drop_data(this);
            free_local_parser = true;
            i_ret = -1;
            break;
        }
        else if (PARSER_STATUS_CONTINUE == ret)
        {
            p_session->discard(this, dir, 0);
            free_local_parser = true;
            break;
        }

        parsed_data = true;
        data_offset += pkt_len;
        if (data_offset == data_len)
        {
            break;
        }

        pkt_len = read_mysql_packet_length(data + data_offset);
        // seqid = (int)(*(data + 3));
        data_offset += 4;
    } while (1);

    if (parsed_data)
    {
        // 第一个数据包是服务器请求包，不需要存入session
        // 最后退出连接的请求也不需要处理
        if (PROTO_CONN_INIT != curr_conn_stat &&
            p_mpd->op_code != COM_QUIT)
        {
            __sync_fetch_and_add(&m_stats_mysql.cnt_parser_total, 1);

            if (STREAM_REQ == dir && p_mpd->op_code >= COM_SLEEP && p_mpd->op_code <= COM_RESET_CONNECTION)
            {
                __sync_fetch_and_add(&m_stats_mysql.cnt_command, 1);
            }

            p_mpd->user = &p_my_status->user;
            p_mpd->password = &p_my_status->password;
            p_mpd->db_name = &p_my_status->db_name;
            p_mpd->server_version = &p_my_status->server_version;
            p_mpd->resource_type = RESOURCE_TYPE_MYSQL;
            p_mpd->client_ip = p_my_status->client_ip;
            p_mpd->client_port = p_my_status->client_port;
            p_mpd->server_ip = p_my_status->server_ip;
            p_mpd->server_port = p_my_status->server_port;
            p_mpd->c_mac = (const char *)pcon->client_mac;
            p_mpd->s_mac = (const char *)pcon->server_mac;
            if (dir)
            {
                p_mpd->resp_size = data_len;
            }
            else
            {
                p_mpd->req_size = data_len;
            }

            if (!mysql_parser_merge(p_stream, p_mpd, CONTR_DIR(dir)))
            {
                // 保存没匹配成功的解析结果
                insert_into_parser_header(p_stream, dir, p_hlf_stream);
                p_session->update_time();           // 更新当前会话时间
            }
            else
            {
                // 成功匹配，释放parser
                free_local_parser = true;
            }
        }
        else
        {
            free_local_parser = true;
        }
        if (comp_data_offset)
        {
            // 如果是压缩数据，则comp_data_offset > 0，不能用解压缩的offset移动数据
            data_offset = comp_data_offset;
        }
        p_session->discard(this, dir, data_offset);
    }

    if (free_local_parser)
    {
        del_mysql_parsed_data(&p_hlf_stream->data, false);
        free(p_hlf_stream);
    }
    if (uncomp_data)
    {
        free((void *)uncomp_data);
    }
    GWLOG_DEBUG(m_comm, "end mysql_callback\n\n\n");

    return 0;
}

int CMysqlParser::parse_clear(CSessionMgt *psm, const app_stream_t *a_app, const struct conn *pcon)
{
    return 0;
}

/**
 * 在连接关闭时，解析数据流协议。
 * @param CSessionMgt *
 * @param app_stream_t *
 * @param struct conn *
 */
int CMysqlParser::parse_on_close(CSessionMgt *, const app_stream_t *, const struct conn *)
{
    __sync_fetch_and_add(&m_stats_mysql.cnt_session_closed, 1);
    return 0;
}

/**
 * 在连接重置时，解析数据流协议。
 * @param CSessionMgt *
 * @param app_stream_t *
 * @param struct conn *
 */
int CMysqlParser::parse_on_reset(CSessionMgt *, const app_stream_t *, const struct conn *)
{
  return 0;
}

bool CMysqlParser::is_mysql_protocol(CSession *p_sess, int dir)
{
    if (NULL == p_sess)
    {
        return false;
    }

    int data_len = 0;
    const char *pkt = p_sess->get_data(this, dir, &data_len, NULL);

    if (NULL != pkt && data_len > 6/* 至少有6B */)
    {
        // mysql数据包的payload长度
        uint32_t pkt_len = read_mysql_packet_length(pkt);
        uint8_t seq_id = (uint8_t)(*(pkt + 3));

        pkt += 4;       // move to payload
        if (pkt_len + 4 == data_len)
        {
            if (STREAM_RSP == dir)
            {
                /* 响应侧 */
                // mysql第一个响应包的seq id为0
                if (seq_id == 0 && ((*pkt) == 0x09 || (*pkt) == 0x0a))
                {
                    // 第5个字节为协议版本号 0x09 或 0x0a
                    return true;
                }
            }
            else
            {
                /* 请求侧 */
                // mysql第一个请求包的seq id为1, 至少32B
                char reserved[23] = {0};
                if (seq_id == 1
                    && data_len > 32
                    && ((*(pkt + 1)) & 0x02)/* CLIENT_PROTOCOL_41 always set */
                    && !memcmp(pkt + 9, reserved, 23)/* 预留23B为0 */)
                {
                    return true;
                }
            }
        }
    }

    return false;
}

void CMysqlParser::keep_bstring_data(b_string_t *bstr)
{
    if (bstr->s && bstr->len > 0)
    {
        char *tmp = (char *)malloc(bstr->len);
        memcpy(tmp, bstr->s, bstr->len);
        bstr->s = tmp;
    }
}

void CMysqlParser::set_ip_port(const struct conn *p_conn, mysql_status_t *p_status)
{
    if (p_conn->client.v != 6)
    {
        // ipv4
        strcpy(p_status->client_ip, inet_ntoa(*(struct in_addr*)&p_conn->client.ipv4));
    }
    else
    {
        // ipv6
        get_ip6addr_str((uint32_t *)p_conn->client.ipv6, p_status->client_ip, sizeof(p_status->client_ip) - 1);
    }

    if (p_conn->server.v != 6)
    {
        // ipv4
        strcpy(p_status->server_ip, inet_ntoa(*(struct in_addr*)&p_conn->server.ipv4));
    }
    else
    {
        // ipv6
        get_ip6addr_str((uint32_t *)p_conn->server.ipv6, p_status->server_ip, sizeof(p_status->server_ip) - 1);
    }

    // port
    p_status->client_port = p_conn->client.port;
    p_status->server_port = p_conn->server.port;
}

StreamData *CMysqlParser::get_stream_data_from_session(CSession *p_session, int dir)
{
    StreamData *p_stream_data = NULL;

    if ((p_stream_data = p_session->get_stream_data_from_parser(this)) == NULL)
    {
        // 创建 StreamData
        p_stream_data = new StreamData();
        if (!p_session->set_parser(this, p_stream_data))
        {
            delete p_stream_data;
            return NULL;
        }

        p_stream_data->p_mysql_stream = new mysql_stream_t();

        SessionMgtData *p_sess_mgt_data = p_session->get_session_mgt()->get_session_data_from_parser(this);
        if (p_sess_mgt_data == NULL)
        {
            p_sess_mgt_data = new SessionMgtData;
            if (!p_session->get_session_mgt()->set_parser_data(this, p_sess_mgt_data))
            {
                delete p_sess_mgt_data;
            }
        }

        p_sess_mgt_data = p_session->get_session_mgt()->get_session_data_from_parser(this);
    }

    p_session->update_time(); // 更新当前会话时间

    return p_stream_data;
}

/**
 * 释放session中最新分配的节点数据
 */
void CMysqlParser::free_mysql_parsed_data_in_session(mysql_stream_t *p_stream, int a_dir)
{
    if (!p_stream)
    {
        return;
    }

    mysql_half_stream_t *p = NULL;
    if (STREAM_REQ == a_dir)
    {
        p = p_stream->p_mysql_server;
        if (p)
        {
            if (p == p_stream->p_mysql_server_last)
            {
                // 只有一个节点
                p_stream->p_mysql_server_last = NULL;
            }

            p_stream->p_mysql_server = p->next;
        }
    }
    else
    {
        p = p_stream->p_mysql_client;
        if (p)
        {
            if (p == p_stream->p_mysql_client_last)
            {
                // 只有一个节点
                p_stream->p_mysql_client_last = NULL;
            }

            p_stream->p_mysql_client = p->next;
        }
    }

    if (p)
    {
        del_mysql_parsed_data(&p->data, true);
        free(p);
    }
}

/**
 * 将请求侧数据与响应侧数据进行合并
 */
mysql_parsed_data_t *CMysqlParser::mysql_parsed_data_merge(mysql_parsed_data_t *p_dst,
                                                            mysql_parsed_data_t *p_src,
                                                            int src_dir)
{
    if (p_dst && p_src)
    {
        if (p_src->sql.s)
        {
            p_dst->sql.s = p_src->sql.s;
            p_dst->sql.len = p_src->sql.len;
        }
        if (p_src->op_code)
        {
            p_dst->op_code = p_src->op_code;
        }
        if (p_src->sub_cmd)
        {
            p_dst->sub_cmd = p_src->sub_cmd;
        }
        if (p_src->stmt_id)
        {
            p_dst->stmt_id = p_src->stmt_id;
        }
        if (p_src->conn_id)
        {
            p_dst->conn_id = p_src->conn_id;
        }
        if (p_src->rows)
        {
            p_dst->rows = p_src->rows;
        }
        if (p_src->col_def)
        {
            p_dst->col_def = p_src->col_def;
        }
        if (p_src->col_cnt)
        {
            p_dst->col_cnt = p_src->col_cnt;
        }
        if (p_src->rs_rows)
        {
            p_dst->rs_rows = p_src->rs_rows;
        }
        if (p_src->row_cnt)
        {
            p_dst->row_cnt = p_src->row_cnt;
        }
        if (p_src->success)
        {
            p_dst->success = p_src->success;
        }
        if (p_src->err_code)
        {
            p_dst->err_code = p_src->err_code;
        }
        if (p_src->err_msg)
        {
            p_dst->err_msg = p_src->err_msg;
        }

        if (src_dir == STREAM_REQ)
        {
            p_dst->req_time = p_dst->tm - p_src->tm;
            p_dst->tm = p_src->tm;
            p_dst->req_size = p_src->req_size;
        }
        else
        {
            p_dst->req_time = p_src->tm - p_dst->tm;
            p_dst->resp_size = p_src->resp_size;
        }

        // generate uuid
        uuid_t uu;
        uuid_generate(uu);
        uuid_unparse(uu, p_dst->id);
    }

    return p_dst;
}

char *CMysqlParser::bstring_to_cjson_str(const b_string_t *bstr)
{
    char *jstr = NULL;
    if (bstr->s)
    {
        char *s = strndup(bstr->s, MIN(bstr->len, MAX_COL_VAL_LEN));
        jstr = cJSON_EscapeString(s);
        free(s);
    }
    return jstr;
}

char *CMysqlParser::mysql_json_dump_event(const mysql_parsed_data_t *p_mpd)
{
    char *p_buf = NULL;
    if (p_mpd)
    {
        int offset = 0;
        int buf_len = 1024;
        int id_len = strlen(p_mpd->id);

        buf_len += id_len;
        buf_len += p_mpd->user->len;
        buf_len += p_mpd->password->len;
        buf_len += p_mpd->db_name->len;
        buf_len += p_mpd->server_version->len;
        buf_len += p_mpd->sql.len;

        p_buf = (char *)malloc(buf_len);
        if (NULL == p_buf)
        {
            return NULL;
        }

        if (id_len > 0)
        {
            offset += sprintf(p_buf, "{\"id\":\"%s\",\"sessionId\":null,", p_mpd->id);
        }
        else
        {
            offset += sprintf(p_buf, "{\"id\":null,\"sessionId\":null,");
        }
        offset += sprintf(p_buf + offset, "\"success\":%d,", p_mpd->success);
        offset += sprintf(p_buf + offset, "\"errCode\":%d,", p_mpd->err_code);
        if (p_mpd->err_msg)
        {
            char *tmp = cJSON_EscapeString(p_mpd->err_msg);
            offset += sprintf(p_buf + offset, "\"errMsg\":%s,", tmp);
            if (tmp)
            {
                cJSON_free(tmp);
            }
        }
        else
        {
            offset += sprintf(p_buf + offset, "\"errMsg\":null,");
        }
        offset += sprintf(p_buf + offset, "\"resourceType\":%d,", p_mpd->resource_type);
        if (p_mpd->user->s)
        {
            char *jstr = bstring_to_cjson_str(p_mpd->user);
            offset += sprintf(p_buf + offset, "\"username\":%s,", jstr);
            if (jstr)
            {
                cJSON_free(jstr);
            }
        }
        else
        {
            offset += sprintf(p_buf + offset, "\"username\":\"anonymous\",");
        }
        if (p_mpd->db_name->s)
        {
            char *jstr = strndup(p_mpd->db_name->s, p_mpd->db_name->len);
            offset += sprintf(p_buf + offset, "\"dbName\":\"%s\",", jstr);
            free(jstr);
        }
        else
        {
            offset += sprintf(p_buf + offset, "\"dbName\":null,");
        }
        if (p_mpd->sql.s)
        {
            char *jstr = bstring_to_cjson_str(&p_mpd->sql);
            offset += sprintf(p_buf + offset, "\"reqContent\":%s,", jstr);
            if (jstr)
            {
                cJSON_free(jstr);
            }
        }
        else
        {
            offset += sprintf(p_buf + offset, "\"reqContent\":null,");
        }
        offset += sprintf(p_buf + offset, "\"timestamp\":\"%ld\",", p_mpd->tm);
        if (strlen(p_mpd->client_ip) > 0)
        {
            offset += sprintf(p_buf + offset, "\"ip\":\"%s\",", p_mpd->client_ip);
        }
        else
        {
            offset += sprintf(p_buf + offset, "\"ip\":null,");
        }
        offset += sprintf(p_buf + offset, "\"port\":%d,", p_mpd->client_port);
        // if (p_mpd->operation)
        // {
        //     offset += sprintf(p_buf + offset, "\"operation\":\"%s\",", p_mpd->operation);
        // }
        // else
        {
            offset += sprintf(p_buf + offset, "\"operation\":null,");
        }
        if (strlen(p_mpd->server_ip) > 0)
        {
            offset += sprintf(p_buf + offset, "\"serverIp\":\"%s\",", p_mpd->server_ip);
        }
        else
        {
            offset += sprintf(p_buf + offset, "\"serverIp\":null,");
        }
        offset += sprintf(p_buf + offset, "\"serverPort\":%d,", p_mpd->server_port);
        if (p_mpd->server_version->s)
        {
            char *jstr = strndup(p_mpd->server_version->s, p_mpd->server_version->len);
            offset += sprintf(p_buf + offset, "\"version\":\"%s\",\"serverVersion\":\"%s\",", jstr, jstr);
            free(jstr);
        }
        else
        {
            offset += sprintf(p_buf + offset, "\"version\":null,\"serverVersion\":null,");
        }

        offset += sprintf(p_buf + offset, "\"serverMac\":\"%s\",", p_mpd->s_mac);
        offset += sprintf(p_buf + offset, "\"clientMac\":\"%s\",", p_mpd->c_mac);
        offset += sprintf(p_buf + offset, "\"reqSize\":%llu,", p_mpd->req_size);
        offset += sprintf(p_buf + offset, "\"respSize\":%llu,", p_mpd->resp_size);
        offset += sprintf(p_buf + offset, "\"reqTime\":%llu,", p_mpd->req_time);
        offset += sprintf(p_buf + offset, "\"sessionCodingType\":null");
        offset += sprintf(p_buf + offset, "}");
    }

    return p_buf;
}

char *CMysqlParser::mysql_value_convert_to_string(b_string_t *val, column_def_t *col_def)
{
    if (NULL == val || NULL == col_def)
    {
        return NULL;
    }
    char *p_buf;
    p_buf = bstring_to_cjson_str(val);

    // switch(col_def->column_type)
    // {
    // case MYSQL_TYPE_DECIMAL:
    // case MYSQL_TYPE_TINY:
    // case MYSQL_TYPE_SHORT:
    // case MYSQL_TYPE_LONG:
    // case MYSQL_TYPE_LONGLONG:
    // case MYSQL_TYPE_INT24:
    //     p_buf = (char *)malloc(64);
    //     memset(p_buf, 0, 64);
    //     sprintf(p_buf, "%ld", val->s);
    //     break;
    // case MYSQL_TYPE_VARCHAR:
    // case MYSQL_TYPE_VAR_STRING:
    // case MYSQL_TYPE_STRING:
    // case MYSQL_TYPE_TINY_BLOB:
    // case MYSQL_TYPE_MEDIUM_BLOB:
    // case MYSQL_TYPE_LONG_BLOB:
    // case MYSQL_TYPE_BLOB:
    //     p_buf = bstring_to_cjson_str(val);
    //     break;
    // case MYSQL_TYPE_FLOAT:
    // case MYSQL_TYPE_DOUBLE:
    //     break;
    // case MYSQL_TYPE_NULL:
    //     break;
    // case MYSQL_TYPE_TIMESTAMP:
    // case MYSQL_TYPE_DATE:
    // case MYSQL_TYPE_TIME:
    // case MYSQL_TYPE_DATETIME:
    // case MYSQL_TYPE_YEAR:
    // case MYSQL_TYPE_NEWDATE:
    // case MYSQL_TYPE_TIMESTAMP2:
    // case MYSQL_TYPE_DATETIME2:
    // case MYSQL_TYPE_TIME2:
    //     break;
    // case MYSQL_TYPE_NEWDECIMAL:
    // case MYSQL_TYPE_BIT:
    // case MYSQL_TYPE_ENUM:
    // case MYSQL_TYPE_SET:
    // case MYSQL_TYPE_GEOMETRY:
    //     break;
    // }
    return p_buf;
}

char *CMysqlParser::mysql_json_dump_response(const mysql_parsed_data_t *p_mpd)
{
    int i;
    char *p_buf = NULL;
    int buf_len = 512;
    column_def_t *col_def = p_mpd->col_def;
    mysql_row_data_t **rs_rows = p_mpd->rs_rows;
    int col_cnt = p_mpd->col_cnt;
    int row_cnt = p_mpd->row_cnt;
    int offset_rows = 0;

    char p_buf_rows[1024 * 1024 * 4] = {0};
    if (row_cnt && col_cnt)
    {

        offset_rows += sprintf(p_buf_rows, "[");
        for (i = 0; i < row_cnt; i++)
        {
            int j;
            int offset_row = 0;
            mysql_row_data_t *p_row = *(rs_rows + i);
            b_string_t ** d = p_row->row;

            char p_buf_row[1024] = {0};
            offset_row += sprintf(p_buf_row, "[");
            for (j = 0; j < col_cnt; j++)
            {
                // 对应字段属性
                // column_def_t *p_tmp_col = *(col_def + j);

                // 字段值
                b_string_t *col_val = *(p_row->row + j);
                if ((col_val->len == 1) && ((uint8_t)(*(col_val->s)) == 0xfb))
                {
                    offset_row += sprintf(p_buf_row + offset_row, "\"NULL\",");
                }
                else
                {

                    char * val_str = mysql_value_convert_to_string(col_val, col_def);
                    offset_row += sprintf(p_buf_row + offset_row, "%s,", val_str ? val_str : "null");
                    if (val_str)
                    {
                        cJSON_free(val_str);
                    }
                }
            }
            offset_row += sprintf(p_buf_row + offset_row - 1, "]");

            offset_rows += sprintf(p_buf_rows + offset_rows, "%s,", p_buf_row);
        }
        offset_rows += sprintf(p_buf_rows + offset_rows - 1, "]");
    }

    // TODO 没有记录时时候需要返回字段？？
    if (col_cnt)
    {
        char tmp[128] = {0};
        char *p_jstr;

        char p_buf_co[1024] = {0};              // column
        char p_buf_ty[1024] = {0};              // type
        char p_buf_sc[1024] = {0};              // schema
        char p_buf_ta[1024] = {0};              // table
        char p_buf_ch[1024] = {0};              // charset
        char p_buf_dv[1024*4] = {0};              // default value

        int offset_co = 0;
        int offset_ty = 0;
        int offset_sc = 0;
        int offset_ta = 0;
        int offset_ch = 0;
        int offset_dv = 0;

        offset_co += sprintf(p_buf_co, "[");
        offset_ty += sprintf(p_buf_ty, "[");
        offset_sc += sprintf(p_buf_sc, "[");
        offset_ta += sprintf(p_buf_ta, "[");
        offset_ch += sprintf(p_buf_ch, "[");
        offset_dv += sprintf(p_buf_dv, "[");

        while (col_def)
        {
            // column_def_t *p_def = *(col_def + i);
            column_def_t *p_def = col_def;

            // column name
            if (p_def->column.s)
            {
                memset(tmp, 0, sizeof(tmp));
                memcpy(tmp, p_def->column.s, p_def->column.len);
                offset_co += sprintf(p_buf_co + offset_co, "\"%s\",", tmp);
            }
            else
            {
                offset_co += sprintf(p_buf_co + offset_co, "null,");
            }

            // schema
            if (p_def->schema.s)
            {
                memset(tmp, 0, sizeof(tmp));
                memcpy(tmp, p_def->schema.s, p_def->schema.len);
                offset_sc += sprintf(p_buf_sc + offset_sc, "\"%s\",", tmp);
            }
            else
            {
                offset_sc += sprintf(p_buf_sc + offset_sc, "null,");
            }

            // table name
            if (p_def->table.s)
            {
                memset(tmp, 0, sizeof(tmp));
                memcpy(tmp, p_def->table.s, p_def->table.len);
                offset_ta += sprintf(p_buf_ta + offset_ta, "\"%s\",", tmp);
            }
            else
            {
                offset_ta += sprintf(p_buf_ta + offset_ta, "null,");
            }

            // column type
            offset_ty += sprintf(p_buf_ty + offset_ty, "%d,", p_def->column_type);

            // charset
            offset_ch += sprintf(p_buf_ch + offset_ch, "\"%s\",", to_str_charset(p_def->charset));

            // default value
            if (p_def->default_val.s)
            {
                if ((p_def->default_val.len == 1) && ((uint8_t)(*(p_def->default_val.s)) == 0xfb))
                {
                    offset_dv += sprintf(p_buf_dv + offset_dv, "\"NULL\",");
                }
                else
                {
                    char * val_str = mysql_value_convert_to_string(&p_def->default_val, p_def);
                    offset_dv += sprintf(p_buf_dv + offset_dv, "%s,", val_str);
                    if (val_str)
                    {
                        cJSON_free(val_str);
                    }
                }
            }
            else
            {
                offset_dv += sprintf(p_buf_dv + offset_dv, "null,");
            }

            col_def = col_def->next;
        }
        offset_co += sprintf(p_buf_co + offset_co - 1, "]");             // 替换最后一个逗号
        offset_ty += sprintf(p_buf_ty + offset_ty - 1, "]");             // 替换最后一个逗号
        offset_sc += sprintf(p_buf_sc + offset_sc - 1, "]");             // 替换最后一个逗号
        offset_ta += sprintf(p_buf_ta + offset_ta - 1, "]");             // 替换最后一个逗号
        offset_ch += sprintf(p_buf_ch + offset_ch - 1, "]");             // 替换最后一个逗号
        offset_dv += sprintf(p_buf_dv + offset_dv - 1, "]");             // 替换最后一个逗号

        p_buf = (char *)malloc(offset_co + offset_ty + offset_sc + offset_ta + offset_ch + offset_dv + offset_rows + 256);
        sprintf(p_buf, "{\"column\":%s,\"type\":%s,\"schema\":%s,\"table\":%s,\"charset\":%s,\"default\":%s,\"rows\":%s}",
                        p_buf_co,
                        p_buf_ty,
                        p_buf_sc,
                        p_buf_ta,
                        p_buf_ch,
                        p_buf_dv,
                        strlen(p_buf_rows) > 0 ? p_buf_rows : "null");

        char *p_tmp = p_buf;
        p_buf = cJSON_EscapeString(p_buf);
        free(p_tmp);
    }

    return p_buf;
}

char *CMysqlParser::mysql_json_dump_content(const mysql_parsed_data_t *p_mpd)
{
    char *p_buf = NULL;
    if (p_mpd)
    {
        int buf_len = 256;
        int offset = 0;
        int id_len = strlen(p_mpd->id);

        buf_len += id_len;

        char *resp_json = mysql_json_dump_response(p_mpd);
        if (resp_json)
        {
            buf_len += strlen(resp_json);
        }
        p_buf = (char *)malloc(buf_len);
        if (NULL == p_buf)
        {
            cJSON_free(resp_json);
            return NULL;
        }

        if (id_len > 0)
        {
            offset += sprintf(p_buf, "{\"eventId\":\"%s\",", p_mpd->id);
        }
        else
        {
            offset += sprintf(p_buf, "{\"eventId\":null,");
        }
        if (resp_json)
        {
            offset += sprintf(p_buf + offset, "\"responseBody\":%s,", resp_json);
            cJSON_free(resp_json);
        }
        else
        {
            offset += sprintf(p_buf + offset, "\"responseBody\":null,");
        }

        offset += sprintf(p_buf + offset, "\"effectRows\":%d,", p_mpd->row_cnt ? p_mpd->row_cnt : p_mpd->rows);
        offset += sprintf(p_buf + offset, "\"resourceType\":%d,\"timestamp\":\"%ld\"}", p_mpd->resource_type, p_mpd->tm);
    }

    return p_buf;
}

void CMysqlParser::mysql_send_data(const mysql_parsed_data_t *p_result)
{
    char *json_str = NULL;

    // event
    json_str = mysql_json_dump_event(p_result);
    GWLOG_DEBUG(m_comm, "%s event str = %s\n", MYSQL_LOG_PRE, json_str);
    if (json_str)
    {
        mysql_cb_upload_msg(json_str, msg_event_type);
    }

    // content
    json_str = mysql_json_dump_content(p_result);
    GWLOG_DEBUG(m_comm, "%s content str = %s\n", MYSQL_LOG_PRE, json_str);
    if (json_str)
    {
        mysql_cb_upload_msg(json_str, msg_content_type);
    }
}

/**
 * 释放session中被匹配的节点，并调整last指针
 */
void CMysqlParser::free_session_mysql_parser_data(mysql_stream_t *p_stream, int dir, mysql_parsed_data_t *p_data)
{
    mysql_half_stream_t *p;
    mysql_half_stream_t *pp;
    mysql_half_stream_t **root;
    mysql_half_stream_t **last;

    if (p_data == NULL)
    {
        return;
    }

    if (STREAM_REQ == dir)
    {
        root = &p_stream->p_mysql_server;
        last = &p_stream->p_mysql_server_last;
    }
    else
    {
        root = &p_stream->p_mysql_client;
        last = &p_stream->p_mysql_client_last;
    }

    if (*root == NULL)
    {
        return;
    }

    // 找到并释放数据节点
    pp = p = *root;
    while (p != NULL)
    {
        if (p_data == &p->data)
        {
            // 找到对应的数据节点
            break;
        }
        pp = p;
        p = p->next;
    }

    if (p == NULL)
    {
        // 未找到
        return;
    }

    if (*root == p)
    {
        *root = p->next;
    }
    else
    {
        pp->next = p->next;
    }

    if (*root == NULL)
    {
        // 没有节点了
        *last = NULL;
    }
    else if (p->next == NULL)
    {
        // 当前删除的是最后一节点
        *last = pp;
    }

  del_mysql_parsed_data(&p->data, true);
  free(p);
}

bool CMysqlParser::mysql_parser_merge(mysql_stream_t *p_stream, mysql_parsed_data_t *p_data, int from_dir)
{
    int ret = false;

    // 查找对应侧
    mysql_parsed_data_t *p_data_from = NULL;
    mysql_half_stream_t **p_tmp_last = NULL;

    if (from_dir == STREAM_REQ)
    {
        p_tmp_last = &p_stream->p_mysql_server_last;
    }
    else
    {
        p_tmp_last = &p_stream->p_mysql_client_last;
    }

    if (NULL != (*p_tmp_last))
    {
        /* 总是与最先达到的节点匹配 */
        p_data_from = &(*p_tmp_last)->data;
        ret = true;
        GWLOG_DEBUG(m_comm, "%s 找到对应的节点，connection id: %lu, operation: %d\n",
                            MYSQL_LOG_PRE, p_data_from->conn_id, p_data_from->op_code);

        if (p_data->sql.s || p_data_from->sql.s)
        {
#ifdef _DEBUG
            char *sql = (p_data->sql.s) ? strndup(p_data->sql.s, p_data->sql.len) : strndup(p_data_from->sql.s, p_data_from->sql.len);
            GWLOG_DEBUG(m_comm, "%s sql: %s\n", MYSQL_LOG_PRE, sql);
            free(sql);
#endif
            // 转化成json数据上传
            mysql_parsed_data_t *p_data_result = mysql_parsed_data_merge(p_data, p_data_from, from_dir);
            mysql_send_data(p_data_result);

            __sync_fetch_and_add(&m_stats_mysql.cnt_exec_sql, 1);
        }
        else
        {
            GWLOG_DEBUG(m_comm, "%s 不是执行sql的请求，不发送数据\n", MYSQL_LOG_PRE);
        }

        free_session_mysql_parser_data(p_stream, from_dir, p_data_from);
        __sync_fetch_and_add(&m_stats_mysql.cnt_parser_matched, 1);
    }
    else
    {
        GWLOG_DEBUG(m_comm, "%s 没有找到对应的节点\n", MYSQL_LOG_PRE);
    }

    return ret;
}

void CMysqlParser::insert_into_parser_header(mysql_stream_t *p_stream, int dir, mysql_half_stream_t *p_mhs)
{
    if (p_stream && p_mhs)
    {
        int i, j;
        mysql_parsed_data_t *p_data = &p_mhs->data;

        keep_bstring_data(&p_data->sql);

        column_def_t *p_col_def = p_data->col_def;
        while(p_col_def)
        {
            keep_bstring_data(&p_col_def->schema);
            keep_bstring_data(&p_col_def->table);
            keep_bstring_data(&p_col_def->column);
            keep_bstring_data(&p_col_def->default_val);
            p_col_def = p_col_def->next;
        }

        mysql_row_data_t **rs_rows = p_data->rs_rows;
        for (i = 0; i < p_data->row_cnt; i++)
        {
            mysql_row_data_t *row = *(rs_rows + i);
            for (j = 0; j < p_data->col_cnt; j++)
            {
                b_string_t *col_val = *(row->row + j);
                keep_bstring_data(col_val);
            }
        }

        if (STREAM_REQ == dir)
        {
            p_mhs->next = p_stream->p_mysql_server;
            p_stream->p_mysql_server = p_mhs;
            if (NULL == p_stream->p_mysql_server_last)
            {
                p_stream->p_mysql_server_last = p_mhs;
            }
        }
        else
        {
            p_mhs->next = p_stream->p_mysql_client;
            p_stream->p_mysql_client = p_mhs;
            if (NULL == p_stream->p_mysql_client_last)
            {
                p_stream->p_mysql_client_last = p_mhs;
            }
        }
    }
}

void CMysqlParser::mysql_cb_upload_msg(const char *s, const char* msgtype)
{
    if (m_pUpload == NULL)
    {
        GWLOG_INFO(m_comm, "%s upload null\n", MYSQL_LOG_PRE);
        return;
    }

    UploadMsg *pum = new UploadMsg;
    memset(pum, 0, sizeof(UploadMsg));

    pum->cb = sizeof(UploadMsg);
    pum->destroy_func = free_upload_msg_cb;
    pum->parser = this;
    pum->length = strlen(s);
    pum->s = s;
    pum->msgtype = msgtype;
    pum->mem_size = sizeof(UploadMsg) + pum->length;

    m_pUpload->put_msg(pum);
}

void CMysqlParser::free_upload_msg_cb(const struct UploadMsg *p_um)
{
    if (p_um)
    {
        if (p_um->s != NULL)
        {
            free((void *)p_um->s);
        }
        delete p_um;
    }

    // __sync_fetch_and_sub(&g_stats_queue_memory_size, length);
}

// static inline CMysqlParser::void biton_pos()
// {
//     unsigned char c = 0x21;
//     int bit_pos = -1;

//     if (c & 0x01) bit_pos = 0;
//     if (c & 0x02) bit_pos = 1;
//     if (c & 0x04) bit_pos = 2;
//     if (c & 0x08) bit_pos = 3;
//     if (c & 0x10) bit_pos = 4;
//     if (c & 0x20) bit_pos = 5;
//     if (c & 0x40) bit_pos = 6;
//     if (c & 0x80) bit_pos = 7;
// }

/**
 * @return 返回string[NUL]的字节数，包括\0
 */
size_t CMysqlParser::read_string_null(const char *pkt, char **out_str)
{
    assert((pkt != NULL) && (*pkt != 0));

    *out_str = strdup(pkt);
    return strlen(pkt) + 1;
}

/**
 * @return int<lenenc>
 */
uint64_t CMysqlParser::read_lenenc_integer(const char *pkt, size_t len, size_t *offset)
{
    const unsigned char *pos = (const unsigned char *)pkt;
    if (NULL != offset) *offset = 0;

    if (len >= 1 && *pos == 0xfb)
    {
        return 0;
    }
    if (len >= 1 && *pos < 0xfb)
    {
        if (NULL != offset) *offset = 1;
        // ++(*pkt);
        return (uint64_t)*pos;
    }
    if (len >= 3 && *pos == 0xfc)
    {
        if (NULL != offset) *offset = 3;
        // (*pkt) += 3;
        return (uint64_t)read_uint2(pkt + 1);
    }
    if (len >= 4 && *pos == 0xfd)
    {
        if (NULL != offset) *offset = 4;
        // (*pkt) += 4
        return (uint64_t)read_uint4(pkt + 1);
    }
    if (len >= 9 && *pos == 0xfe)
    {
        if (NULL != offset) *offset = 9;
        // (*pkt) += 9;
        return (uint64_t)read_uint8(pkt + 1);
    }

    return 0;
}

/**
 * As of MySQL 5.7.5, OK packes are also used to indicate EOF, and EOF packets are deprecated
 */
int CMysqlParser::unpack_ok_packet(const char *pkt, size_t len, mysql_status_t *p_my_status, mysql_parsed_data_t *p_result)
{
    uint64_t offset = 1;
    size_t lenenc = 0;
    uint16_t status_flags = 0x0;
    uint16_t warnings = 0;
    char *info = NULL;
    const char *pos = pkt;
    
    uint64_t affected_rows = read_lenenc_integer(pos + offset, len - offset, &lenenc);
    offset += lenenc;
    uint64_t insert_id = read_lenenc_integer(pos + offset, len - offset, &lenenc);
    offset += lenenc;

    if (can_client_protocol_41(p_my_status))
    {
        status_flags = read_uint2(pos + offset);
        offset += 2;
        warnings = read_uint2(pos + offset);
        offset += 2;
    }
    else if ((p_my_status->server_cap_flags & CLIENT_TRANSACTIONS) &&
            (p_my_status->client_cap_flags & CLIENT_TRANSACTIONS))
    {
        status_flags = read_uint2(pos + offset);
        offset += 2;
    }

    // info
    if ((p_my_status->server_cap_flags & CLIENT_SESSION_TRACK) &&
        (p_my_status->client_cap_flags & CLIENT_SESSION_TRACK))
    {
        uint64_t info_len = read_lenenc_integer(pos + offset, len - offset, &lenenc);
        offset += lenenc;
        if (info_len)
        {
            info = strndup(pos + offset, info_len);
        }
        offset += info_len;

        // session state change
        if (status_flags & SERVER_SESSION_STATE_CHANGED)
        {
            // TODO
            // Session State Information
            info_len = read_lenenc_integer(pos + offset, len - offset, &lenenc);
            offset += lenenc;
            if (info_len > 0)
            {
                // 1B type + lenB data + strlen B + str   array ?
                uint8_t change_type = (uint8_t)(*(pos + offset));
                offset++;
                uint64_t change_data_len = read_lenenc_integer(pos + offset, len - offset, &lenenc);
                offset += lenenc;
                if (change_data_len > 0)
                {
                    switch(change_type)
                    {
                    case SESSION_TRACK_SYSTEM_VARIABLES:
                        // string<lenenc>   name
                        // string<lenenc>  value
                        break;
                    case SESSION_TRACK_SCHEMA:
                    {
                        uint64_t name_len = read_lenenc_integer(pos + offset, change_data_len, &lenenc);
                        if (name_len)
                        {
                            if (p_my_status->db_name.s)
                            {
                                free((void *)p_my_status->db_name.s);
                            }
                            p_my_status->db_name.s = strndup(pos + offset + lenenc, name_len);
                            p_my_status->db_name.len = name_len;
                        }
                        break;
                    }
                    case SESSION_TRACK_STATE_CHANGE:
                        break;
                    case SESSION_TRACK_GTIDS:
                        break;
                    }
                }
                offset += change_data_len;
            }
        }
    }
    else
    {
        size_t rest = len - offset;
        if (rest > 0)
        {
            info = strndup(pos + offset, rest); 
        }
    }

    GWLOG_DEBUG(m_comm, "%s affected_rows: %d, insert id: %d, status flags: %.4x, warnings: %d, info: %s\n",
                        MYSQL_LOG_PRE, affected_rows, insert_id, status_flags, warnings, info);

    if (info)
    {
        free(info);
    }

    return 0;
}

int CMysqlParser::unpack_err_packet(const char *pkt, size_t len, mysql_status_t *p_my_status, mysql_parsed_data_t *p_result)
{
    int offset = 1;
    p_result->err_code = read_uint2(pkt + offset);
    offset += 2;
    if (can_client_protocol_41(p_my_status))
    {
        char sql_state[5 + 1] = {0};
        char sql_state_marker = *(pkt + offset);
        offset += 1;

        memcpy(sql_state, pkt + offset, 5);
        offset += 5;

        GWLOG_DEBUG(m_comm, "%s sql state marker: %c, sql state: %s\n", MYSQL_LOG_PRE, sql_state_marker, sql_state);
    }

    int err_len = len - offset;
    if (err_len > 0)
    {
        p_result->err_msg = strndup(pkt + offset, err_len);
        GWLOG_DEBUG(m_comm, "%s err_code: %d, error message: %s\n", MYSQL_LOG_PRE, p_result->err_code, p_result->err_msg);
    }

    p_result->success = 0;

    return 0;
}

int CMysqlParser::unpack_eof_packet(const char *pkt, size_t len, mysql_status_t *p_my_status, mysql_parsed_data_t *p_result)
{
    if (can_client_protocol_41(p_my_status))
    {
        int warnings = read_uint2(pkt + 1);
        int status = read_uint2(pkt + 3);
        GWLOG_DEBUG(m_comm, "%s number of warnings: %d, status flags: %#.8x\n", MYSQL_LOG_PRE, warnings, status);
    }
    return 0;
}

int CMysqlParser::response_packet_type(const char *pkt, size_t len)
{
    const unsigned char *pos = (const unsigned char *)pkt;
    if (len <= 9 && *pos == 0xfe) return MYSQL_RESPONSE_PACKET_EOF;
    if ((len >= 7 && *pos == 0x0) || (len > 9 && *pos == 0xfe)) return MYSQL_RESPONSE_PACKET_OK;
    if (len >= 1 && *pos == 0xff) return MYSQL_RESPONSE_PACKET_ERR;

    return MYSQL_RESPONSE_PACKET_DATA;
}

int CMysqlParser::unpack_com_query(const char *pkt, size_t len, mysql_parsed_data_t *p_result)
{
    p_result->sql.s = pkt + 1;
    p_result->sql.len = len - 1;
    p_result->op_code = COM_QUERY;
    return PARSER_STATUS_FINISH;
}

int CMysqlParser::unpack_column_def_41(const char *pkt, size_t len, mysql_status_t *p_my_status, column_def_t *p_result)
{
    size_t offset = 0, enc_offset;
    uint64_t enclen;

    // catalog, always 'def'
    offset += 4;

    // schema name
    enclen = read_lenenc_integer(pkt + offset, len - offset, &enc_offset);
    if (enclen > 0)
    {
        p_result->schema.s = pkt + offset + enc_offset;
        p_result->schema.len = enclen;
    }
    offset += enclen + enc_offset;

    // virtual table name
    enclen = read_lenenc_integer(pkt + offset, len - offset, &enc_offset);
    if (enclen > 0)
    {
        p_result->table.s = pkt + offset + enc_offset;
        p_result->table.len = enclen;
    }
    offset += enclen + enc_offset;

    // physical table name
    enclen = read_lenenc_integer(pkt + offset, len - offset, &enc_offset);
    offset += enclen + enc_offset;

    // virtual column name
    enclen = read_lenenc_integer(pkt + offset, len - offset, &enc_offset);
    if (enclen > 0)
    {
        p_result->column.s = pkt + offset + enc_offset;
        p_result->column.len = enclen;
    }
    offset += enclen + enc_offset;

    // pyhsical column name
    enclen = read_lenenc_integer(pkt + offset, len - offset, &enc_offset);
    offset += enclen + enc_offset;

    // length of the following fields (always 0x0c)
    ++offset;

    // column charset
    p_result->charset = read_uint2(pkt + offset);
    offset += 2;

    // maximum length of the field
    p_result->col_max_len = (uint32_t)read_uint4(pkt + offset);
    offset += 4;

    // type of the column
    p_result->column_type = (uint8_t)(*(pkt + offset));
    offset += 1;

    // 2B flags + 1B decimals + 2B filter
    offset += 5;

    // command was COM_FIELD_LIST
    // lenenc_int     length of default-values
    // string[$len]   default values
    if (len > offset)
    {
        if (*((uint8_t *)pkt + offset) == 0xfb)
        {
            p_result->default_val.s = pkt + offset;
            p_result->default_val.len = 1;
            offset++;
        }
        else
        {
            enclen = read_lenenc_integer(pkt + offset, len - offset, &enc_offset);
            if (enclen > 0)
            {
                p_result->default_val.s = pkt + offset + enc_offset;
                p_result->default_val.len = enclen;
            }
            offset += enclen + enc_offset;
        }
    }

    return offset;
}

int CMysqlParser::unpack_column_def_320(const char *pkt, size_t len, mysql_status_t *p_my_status, column_def_t *p_result)
{
    size_t offset = 0, enc_offset;
    uint64_t enclen;

    // table name
    enclen = read_lenenc_integer(pkt + offset, len - offset, &enc_offset);
    if (enclen > 0)
    {
        p_result->table.s = pkt + offset + enc_offset;
        p_result->table.len = enclen;
    }
    offset += enclen + enc_offset;

    // column name
    enclen = read_lenenc_integer(pkt + offset, len - offset, &enc_offset);
    if (enclen > 0)
    {
        p_result->column.s = pkt + offset + enc_offset;
        p_result->column.len = enclen;
    }
    offset += enclen + enc_offset;

    // column length
    p_result->col_max_len = (uint32_t)read_uint4(pkt + offset);
    offset += 4;

    // column type
    p_result->column_type = (uint8_t)(*(pkt + offset + 1));
    offset += 2;

    if ((p_my_status->client_cap_flags & CLIENT_LONG_FLAG) && (p_my_status->server_cap_flags & CLIENT_LONG_FLAG))
    {
        // 1B encLength + 2B flags + 1B decimals
        offset += 4;
    }
    else
    {
        // 1B encLength + 1B flags + 1B decimals
        offset += 3;
    }

    // command was COM_FIELD_LIST
    // lenenc_int     length of default-values
    // string[$len]   default values
    if (len > offset)
    {
        if (*((uint8_t *)pkt + offset) == 0xfb)
        {
            p_result->default_val.s = pkt + offset;
            p_result->default_val.len = 1;
            offset++;
        }
        else
        {
            enclen = read_lenenc_integer(pkt + offset, len - offset, &enc_offset);
            if (enclen > 0)
            {
                p_result->default_val.s = pkt + offset + enc_offset;
                p_result->default_val.len = enclen;
            }
            offset += enclen + enc_offset;
        }
    }

    return offset;
}

// static int unpack_com_stmt_prepare(const unsigned char *pkt, size_t len, mysql_parsed_data_t *p_result)
// {
//     return PARSER_STATUS_FINISH;
// }

// static int unpack_com_stmt_close(const unsigned char *pkt, size_t len, mysql_parsed_data_t *p_result)
// {
//     return PARSER_STATUS_FINISH;
// }

// static int unpack_com_stmt_reset(const unsigned char *pkt, size_t len, mysql_parsed_data_t *p_result)
// {
//     return PARSER_STATUS_FINISH;
// }

// static int unpack_com_stmt_fetch(const unsigned char *pkt, size_t len, mysql_parsed_data_t *p_result)
// {
//     return PARSER_STATUS_FINISH;
// }

// static int unpack_com_stmt_send_long_data(const unsigned char *pkt, size_t len, mysql_parsed_data_t *p_result)
// {
//     return PARSER_STATUS_FINISH;
// }

// static int unpack_com_stmt_execute(const unsigned char *pkt, size_t len, mysql_parsed_data_t *p_result)
// {
//     return PARSER_STATUS_FINISH;
// }

int CMysqlParser::unpack_com_field_list(const char *pkt, size_t len, mysql_parsed_data_t *p_result)
{
    const char *show_field_prefix = "show fields from ";
    const char *tmp_tbl = pkt + 1;
    int tbl_len = strlen(tmp_tbl);
    int sql_len = strlen(show_field_prefix) + tbl_len + 1;
    char *sql = (char *)malloc(sql_len);
    sprintf(sql, "%s%s", show_field_prefix, tmp_tbl);

    if (tbl_len + 2 < len)
    {
        // TODO ...
        // string[EOF], field wildcard
    }
    p_result->sql.s = sql;
    p_result->sql.len = sql_len;

    return PARSER_STATUS_FINISH;
}

bool CMysqlParser::is_column_def_packet(const char *pkt, size_t len, const mysql_status_t *p_status)
{
    int ret = false;
    if (PROTO_CONN_FIELD_PACKET != p_status->conn_stat
        && PROTO_CONN_RESPONSE_SHOW_FIELDS != p_status->conn_stat)
    {
        return false;
    }

    if (can_client_protocol_41(p_status))
    {
        if (len > 19 && !memcmp(pkt, "\x03\x64\x65\x66", 4)/* \x03def */)
        {
            ret = true;
        }
    }
    else
    {
        // TODO 320情况下需要更精确
        if (len > 13)
        {
            ret = true;
        }

    }
    return ret;
}

void CMysqlParser::append_column_def(mysql_parsed_data_t *p_result, column_def_t *p_col)
{
    column_def_t *p = p_result->col_def;
    if (NULL == p)
    {
        p_result->col_def = p_col;
    }
    else
    {
        /* find the last column */
        while (p->next) p = p->next;

        p->next = p_col;
    }
}

int CMysqlParser::unpack_field_packet(const char *pkt, size_t len, mysql_status_t *p_my_status, mysql_parsed_data_t *p_result)
{
    // 查询的字段索引
    int is_client_proto_41 = can_client_protocol_41(p_my_status);

    column_def_t *p_col = (column_def_t *)malloc(sizeof(column_def_t));
    memset(p_col, 0, sizeof(column_def_t));

    p_col->col_idx = p_result->col_cnt;
    if (is_client_proto_41)
    {
        unpack_column_def_41(pkt, len, p_my_status, p_col);
    }
    else
    {
        unpack_column_def_320(pkt, len, p_my_status, p_col);
    }

    append_column_def(p_result, p_col);
    p_result->col_cnt++;

    // if (p_my_status->num_fields == 0)
    // {
    //     p_my_status->conn_stat = PROTO_CONN_ROW_PACKET;
    // }

    return PARSER_STATUS_FINISH;
}

/**
 * row packet:
 * +-------------------+-------+----------------------------
 * | 3B payload length | seqid | string<lenenc> or 0xfb ...
 * +-------------------+-------+----------------------------
 */
int CMysqlParser::unpack_row_packet(const char *pkt, size_t len, mysql_parsed_data_t *p_result)
{
    int i;
    size_t offset = 0;
    int col_cnt = p_result->col_cnt;

    if (col_cnt > 0 && p_result->row_cnt <= MAX_RESP_ROW/* 返回不超过MAX_RESP_ROW行记录 */)
    {
        if (NULL == p_result->rs_rows)
        {
            p_result->rs_rows = (mysql_row_data_t **)malloc(sizeof(mysql_row_data_t *) * MAX_RESP_ROW);
            memset(p_result->rs_rows, 0, sizeof(mysql_row_data_t *) * MAX_RESP_ROW);
        }

        mysql_row_data_t *p_one_row = (mysql_row_data_t *)malloc(sizeof(mysql_row_data_t));
        memset(p_one_row, 0, sizeof(mysql_row_data_t));

        // 分配col_cnt个字段的记录行
        p_one_row->row = (b_string_t **)malloc(sizeof(b_string_t *) * col_cnt);
        memset(p_one_row->row, 0, sizeof(b_string_t *) * col_cnt);
        for (i = 0; i < col_cnt; i++)
        {
            b_string_t *col_val = (b_string_t *)malloc(sizeof(b_string_t));
            memset(col_val, 0, sizeof(b_string_t));
            if ((uint8_t)(*pkt) == 0xfb)
            {
                col_val->s = pkt + offset;
                col_val->len = 1;
                offset += 1;
            }
            else
            {
                size_t enc_offset = 0;
                size_t enclen = read_lenenc_integer(pkt + offset, len - offset, &enc_offset);
                if (enclen > 0)
                {
                    col_val->s = pkt + offset + enc_offset;
                    col_val->len = enclen;
                    offset += enclen + enc_offset;
                }
                else
                {
                    offset += 1;
                }
            }
            *(p_one_row->row + i) = col_val;
        }
        *(p_result->rs_rows + p_result->row_cnt) = p_one_row;
        p_result->row_cnt++;
    }

    return PARSER_STATUS_FINISH;
}

bool CMysqlParser::is_ssl_request(const char *pkt, size_t len)
{
    // 4B Capability
    uint32_t cap_flags = read_uint4(pkt);
    char reserved[23] = {0};
    return len == 32 && (cap_flags & CLIENT_SSL) && !memcmp(pkt + 9, reserved, 23)/* reserved 23B [0] */;
}

/**
 * client response:
 * string[EOF]    auth plugin response
 */
int CMysqlParser::unpack_auth_switch_response(const char *pkt, size_t len)
{
#ifdef _DEBUG
    if (len > 0)
    {
        char *auth = strndup(pkt, len);
        GWLOG_DEBUG(m_comm, "%s auth switch response: %s\n", MYSQL_LOG_PRE, auth);
        free(auth);
    }
#endif
    return PARSER_STATUS_FINISH;
}

/**
 * server request
 */
bool CMysqlParser::is_auth_switch_request(const char *pkt, size_t len)
{
    // 0xfe + plugin name(string[NUl]) + auth(string[EOF])
    // plugin name always mysql_native_password or mysql_old_password ?
    if ((uint8_t)*pkt == 0xfe)
    {
#ifdef _DEBUG
        if (len > 1)
        {
            const char *plugin = pkt + 1;
            int offset = strlen(plugin) + 2;
            char *auth = strndup(pkt + offset, len - offset);
            GWLOG_DEBUG(m_comm, "%s auth switch request --> plugin name: %s, auth: %s\n", MYSQL_LOG_PRE, plugin, auth);

            // TODO
            free(auth);
        }
        else
        {
            // old auth switch request
            GWLOG_DEBUG(m_comm, "%s auth switch request old --> plugin name: mysql_old_password\n", MYSQL_LOG_PRE);
        }
#endif
        return true;
    }
    return false;
}

/**
 * protocol version 9
 */
int CMysqlParser::unpack_handshake_response_320(const char *pkt,
                                                size_t len,
                                                mysql_status_t *p_my_status,
                                                mysql_parsed_data_t *p_result)
{
    int offset = 0;
    char *auth = NULL;

    uint32_t cap_flags = read_uint2(pkt);
    p_my_status->client_cap_flags = cap_flags;
    offset = 5;     // 2B capability + 3B max-packet size

    // username
    // offset += read_string_null(pkt + offset, &usdername);
    p_my_status->user.s = strdup(pkt + offset);
    p_my_status->user.len = strlen(p_my_status->user.s);
    offset += p_my_status->user.len + 1;

    if (cap_flags & CLIENT_CONNECT_WITH_DB)
    {
        // auth response
        offset += read_string_null(pkt + offset, &auth);
        // database
        // offset += read_string_null(pkt + offset, &dbname);
        p_my_status->db_name.s = strdup(pkt + offset);
        p_my_status->db_name.len = strlen(p_my_status->db_name.s);
        offset += p_my_status->db_name.len + 1;
    }
    else
    {
        // auth response
        auth = strndup(pkt + offset, len - offset);
        offset += len - offset;
    }

    GWLOG_DEBUG(m_comm, "%s username: %s, dbname: %s, auth: %s\n",
                        MYSQL_LOG_PRE, p_my_status->user.s, p_my_status->db_name.s, auth);

    if (auth)
    {
        free(auth);
    }

    return PARSER_STATUS_FINISH;
}

/**
 * protocol version 10
 */
int CMysqlParser::unpack_handshake_response_41(const char *pkt,
                                                size_t len,
                                                mysql_status_t *p_my_status,
                                                mysql_parsed_data_t *p_result)
{
    int offset = 0;
    uint32_t cap_flags = read_uint4(pkt);
    p_my_status->client_cap_flags = cap_flags;
    offset += 32;       // skip 4B(capability flags) + 4B(max-packet size) + 1B(character set) + 23B(reserved)

    p_my_status->user.s = strdup(pkt + offset);
    p_my_status->user.len = strlen(p_my_status->user.s);
    offset += p_my_status->user.len + 1;
    GWLOG_DEBUG(m_comm, "%s username: %s\n", MYSQL_LOG_PRE, p_my_status->user.s);

    // auth response
    if (cap_flags & CLIENT_PLUGIN_AUTH_LENENC_CLIENT_DATA)
    {
        size_t enc_offset = 0;
        uint64_t auth_len = read_lenenc_integer(pkt + offset, len - offset, &enc_offset);
#ifdef _DEBUG
        if (auth_len > 0)
        {
            char *auth_reps = (char *)malloc(auth_len + 1);
            memset(auth_reps, 0, auth_len + 1);
            memcpy(auth_reps, pkt + offset + enc_offset, auth_len);
            GWLOG_DEBUG(m_comm, "%s client plugin auth lenenc data: %s, len: %d\n", MYSQL_LOG_PRE, auth_reps, auth_len);
            free(auth_reps);
        }
#endif
        offset += enc_offset + auth_len;
    }
    else if (cap_flags & CLIENT_SECURE_CONNECTION)
    {
        int auth_len = (uint8_t)(*(pkt + offset));
#ifdef _DEBUG
        char *auth_reps = (char *)malloc(auth_len + 1);
        GWLOG_DEBUG(m_comm, "%s client sucure connection auth data: %s, len: %d\n", MYSQL_LOG_PRE, auth_reps, auth_len);
        free(auth_reps);
#endif
        offset += auth_len + 1;
    }
    else
    {
        // string[NUL]
        char *auth_reps = strdup(pkt + offset);
        int auth_len = strlen(auth_reps);
        offset += auth_len + 1;
        GWLOG_DEBUG(m_comm, "%s client auth data: %s, len: %d\n", MYSQL_LOG_PRE, auth_reps, auth_len);
        free(auth_reps);
    }

    // client connect with Database, string[NUL]
    if (cap_flags & CLIENT_CONNECT_WITH_DB)
    {
        p_my_status->db_name.s = strdup(pkt + offset);
        p_my_status->db_name.len = strlen(p_my_status->db_name.s);
        offset += p_my_status->db_name.len + 1;

        GWLOG_DEBUG(m_comm, "%s db name: %s\n", MYSQL_LOG_PRE, p_my_status->db_name.s);
    }

    // client plugin auth, string[NUL]
    if (cap_flags & CLIENT_PLUGIN_AUTH)
    {
        char *auth_name = NULL;
        offset += read_string_null(pkt + offset, &auth_name);
        GWLOG_DEBUG(m_comm, "%s auth_name: %s\n", MYSQL_LOG_PRE, auth_name);

        free(auth_name);
    }

    // last data
    // client connection attributes
    if (cap_flags & CLIENT_CONNECT_ATTRS)
    {
        // parse this data, if need, otherwise skip it.
        uint64_t attrs_len = read_lenenc_integer(pkt + offset, len - offset, NULL);

        GWLOG_DEBUG(m_comm, "%s client connection attributes length: %lld\n", MYSQL_LOG_PRE, attrs_len);
    }

    return PARSER_STATUS_FINISH;
}

/**
 * Login Request, make an response to MySql server
 */
int CMysqlParser::unpack_login_request(const char *pkt,
                                        size_t len,
                                        mysql_status_t *p_my_status,
                                        mysql_parsed_data_t *p_result)
{
    if (p_my_status->protocol_version == 0x0a)
    {
        unpack_handshake_response_41(pkt, len, p_my_status, p_result);
    }
    else
    {
        unpack_handshake_response_320(pkt, len, p_my_status, p_result);
    }
    return PARSER_STATUS_FINISH;
}

/**
 * Initial Handshake Packet
 */
int CMysqlParser::unpack_server_greeting(const char *pkt, size_t len, mysql_status_t *p_my_status)
{
    int offset = 0;
    char *auth_plugin;     // auth plugin min data length 21, part1(8) + part2(> 13)
    uint8_t auth_plugin_len;
    char *auth_plugin_name;

    p_my_status->protocol_version = (uint8_t)*pkt;
    offset++;

    // version number, string[NUL]
    p_my_status->server_version.s = strdup(pkt + offset);
    p_my_status->server_version.len = strlen(p_my_status->server_version.s);
    offset += p_my_status->server_version.len + 1;

    p_my_status->connection_id = read_uint4(pkt + offset);
    offset += 4;

    if (p_my_status->protocol_version == 0x09)
    {
        // Protocol::HandshakeV9
        GWLOG_DEBUG(m_comm, "%s protocol version: %d, server version: %s, connection id: %u\n",
                    MYSQL_LOG_PRE, p_my_status->protocol_version, p_my_status->server_version.s, p_my_status->connection_id);
        return PARSER_STATUS_FINISH;
    }

    // auth plugin data part 1
    auth_plugin = (char *)(pkt + offset);
    offset += 9;

    // upper 2 bytes of capability flags
    p_my_status->server_cap_flags = read_uint2(pkt + offset);
    offset += 2;

    // more data in the packet
    if (len > offset)
    {
        p_my_status->charset = *(pkt + offset);
        offset++;

        // status flags
        uint16_t status_flags = read_uint2(pkt + offset);
        p_my_status->status_flags = status_flags;
        offset += 2;

        // upper 2 bytes of capability flags
        p_my_status->server_cap_flags += read_uint2(pkt + offset) << 16;
        offset += 2;

        // length of auth plugin data
        auth_plugin_len = (uint8_t)(*(pkt + offset));
        offset += 11;                   // skip reserved 10 bytes

        if (auth_plugin_len - 8 > 0)
        {
            if (auth_plugin_len < 21)
            {
                auth_plugin_len = 21; 
            }
            // char *tmp = (char *)malloc(auth_plugin_len + 1);
        }
        offset += MAX(13, auth_plugin_len - 8);
    }

    if (p_my_status->server_cap_flags & CLIENT_PLUGIN_AUTH)
    {
        // string[NUL]
        auth_plugin_name = (char *)(pkt + offset);
    }

    GWLOG_DEBUG(m_comm, "%s protocol version: %d, server version: %s, connection id: %u, auth: %s\n",
            MYSQL_LOG_PRE, p_my_status->protocol_version, p_my_status->server_version.s, p_my_status->connection_id, auth_plugin_name);

    return PARSER_STATUS_FINISH;
}

int CMysqlParser::unpack_local_infile_request(const char *pkt,
                                                size_t len,
                                                mysql_status_t *p_my_status,
                                                mysql_parsed_data_t *p_result)
{

    return PARSER_STATUS_FINISH;
}

int CMysqlParser::unpack_result_set_header(const char *pkt,
                                            size_t len,
                                            mysql_status_t *p_my_status,
                                            mysql_parsed_data_t *p_result)
{

    p_my_status->num_fields = read_lenenc_integer(pkt, len, NULL);

    if (p_my_status->num_fields) {
        p_my_status->conn_stat = PROTO_CONN_FIELD_PACKET;
    } else {
        p_my_status->conn_stat = PROTO_CONN_ROW_PACKET;
    }

    return PARSER_STATUS_FINISH;
}

int CMysqlParser::parse_mysql_request(const char *pkt, size_t len,
                                    mysql_status_t *p_my_status, mysql_parsed_data_t *p_result)
{
    int ret = PARSER_STATUS_FINISH;
    int opcode;
    size_t offset;

    if (p_my_status->conn_stat == PROTO_CONN_AUTH_SWITCH_RESPONSE)
    {
        unpack_auth_switch_response(pkt, len);
        return PARSER_STATUS_FINISH;
    }

    /* deal with Command Phase */
    p_result->op_code = (int)(*pkt);
    switch (p_result->op_code) {
    case COM_QUIT:
        // ret = PARSER_STATUS_DROP_DATA;
        ret = PARSER_STATUS_FINISH;
        GWLOG_DEBUG(m_comm, "%s mysql connection quit\n", MYSQL_LOG_PRE);
        break;

    case COM_PROCESS_INFO:
        p_my_status->conn_stat = PROTO_CONN_RESPONSE_TABULAR;
        break;

    case COM_DEBUG:
    case COM_PING:
        p_my_status->conn_stat = PROTO_CONN_RESPONSE_OK;
        break;

    case COM_STATISTICS:
        p_my_status->conn_stat = PROTO_CONN_RESPONSE_MESSAGE;
        break;

    case COM_INIT_DB:
    case COM_CREATE_DB:
    case COM_DROP_DB:
        // p_my_status->db_name.s = strdup((const char *)(pkt + 1));
        // p_my_status->db_name.len = len - 1;
        p_my_status->conn_stat = PROTO_CONN_RESPONSE_OK;
        break;

    case COM_QUERY:
        p_result->sql.s = pkt + 1;
        p_result->sql.len = len - 1;
        p_my_status->conn_stat = PROTO_CONN_RESPONSE_TABULAR;
        break;

    case COM_STMT_PREPARE:
        p_result->sql.s = pkt + 1;
        p_result->sql.len = len - 1;
        p_my_status->conn_stat = PROTO_CONN_RESPONSE_PREPARE;
        break;

    case COM_STMT_CLOSE:
        p_result->stmt_id = read_uint4(pkt + 1);
        p_my_status->conn_stat = PROTO_CONN_REQUEST;
        break;

    case COM_STMT_RESET:
        p_result->stmt_id = read_uint4(pkt + 1);
        p_my_status->conn_stat = PROTO_CONN_RESPONSE_OK;
        break;

    case COM_FIELD_LIST:
        unpack_com_field_list(pkt, len, p_result);
        p_my_status->conn_stat = PROTO_CONN_RESPONSE_SHOW_FIELDS;
        break;

    case COM_PROCESS_KILL:
        p_result->conn_id = read_uint4(pkt + 1);
        p_my_status->conn_stat = PROTO_CONN_RESPONSE_OK;
        break;

    case COM_CHANGE_USER:
        // user
        if (p_my_status->user.s)
        {
            free((void *)p_my_status->user.s);
        }
        p_my_status->user.s = strdup(pkt + 1);
        p_my_status->user.len = strlen(p_my_status->user.s);
        offset = 1 + p_my_status->user.len + 1;        // cmd + string[NUL]

        if((p_my_status->client_cap_flags & CLIENT_SECURE_CONNECTION) &&
            (p_my_status->server_cap_flags & CLIENT_SECURE_CONNECTION))
        {
            size_t auth_len = (size_t)(*(pkt + offset));
            offset += auth_len;
        }
        else
        {
            size_t auth_len = strlen(pkt + offset);
            offset += auth_len + 1;     // string[NUL]
        }

        p_my_status->conn_stat = PROTO_CONN_RESPONSE_OK;

        if (len > offset)
        {
            // character set
            p_my_status->charset = read_uint2(pkt + offset);
            if ((p_my_status->client_cap_flags & CLIENT_PLUGIN_AUTH) &&
                (p_my_status->server_cap_flags & CLIENT_PLUGIN_AUTH))
            {
                p_my_status->conn_stat = PROTO_CONN_AUTH_SWITCH_REQUEST;
                if ((p_my_status->client_cap_flags & CLIENT_CONNECT_ATTRS) &&
                    (p_my_status->server_cap_flags & CLIENT_CONNECT_ATTRS))
                {
                    // TODO connection attributes
                }
            }
        }
        break;

    case COM_REFRESH:
        p_result->sub_cmd = (uint8_t)(*(pkt + 1));
        p_my_status->conn_stat = PROTO_CONN_RESPONSE_OK;
        break;

    case COM_SHUTDOWN:
        if (len == 2)
        {
            p_result->sub_cmd = (uint8_t)(*(pkt + 1));
        }
        p_my_status->conn_stat = PROTO_CONN_RESPONSE_OK;
        break;

    case COM_SET_OPTION:
        p_result->sub_cmd = (uint8_t)read_uint2(pkt + 1);
        p_my_status->conn_stat = PROTO_CONN_RESPONSE_OK;
        break;

    case COM_STMT_FETCH:
        p_result->stmt_id = read_uint4(pkt + 1);
        p_result->rows = read_uint4(pkt + 5);
        p_my_status->conn_stat = PROTO_CONN_RESPONSE_TABULAR;
        break;

    case COM_STMT_SEND_LONG_DATA:
        p_result->stmt_id = read_uint4(pkt + 1);
        // param_id 4B
        // data
        p_my_status->conn_stat = PROTO_CONN_REQUEST;
        break;

    case COM_STMT_EXECUTE:
        p_result->stmt_id = read_uint4(pkt + 1);
        p_my_status->conn_stat = PROTO_CONN_RESPONSE_TABULAR;
        break;

    case COM_BINLOG_DUMP_GTID:
    case COM_BINLOG_DUMP:
        /* skip parsing */
        p_my_status->conn_stat = PROTO_CONN_REQUEST;
        break;

    case COM_TABLE_DUMP:
    case COM_CONNECT_OUT:
    case COM_REGISTER_SLAVE:
        /* skip parsing */
        p_my_status->conn_stat = PROTO_CONN_REQUEST;
        break;

    default:
        /* undefined command */
        ret = PARSER_STATUS_DROP_DATA;
    }

    return ret;
}

int CMysqlParser::parse_mysql_response(const char *pkt,
                                        size_t len,
                                        mysql_status_t *p_my_status,
                                        mysql_parsed_data_t *p_result)
{
    int ret = PARSER_STATUS_FINISH;
    int resp_type = response_packet_type(pkt, len);
    p_result->success = 1;
    switch (resp_type)
    {
    case MYSQL_RESPONSE_PACKET_OK: 
    case MYSQL_RESPONSE_PACKET_EOF:
        if (resp_type == MYSQL_RESPONSE_PACKET_OK)
        {
            // 遇到第一个OK PACKET后结束connection phase
            if (!p_my_status->is_cmd_phase)
            {
                p_my_status->is_cmd_phase = 1;
            }

            if (PROTO_CONN_AUTH_SWITCH_REQUEST != p_my_status->conn_stat || !is_auth_switch_request(pkt, len))
            {
                unpack_ok_packet(pkt, len, p_my_status, p_result);
            }
        }
        else
        {
            unpack_eof_packet(pkt, len, p_my_status, p_result);
        }

        switch (p_my_status->conn_stat) {
        case PROTO_CONN_FIELD_PACKET:
            p_my_status->conn_stat = PROTO_CONN_ROW_PACKET;
            break;

        case PROTO_CONN_ROW_PACKET:
            if (p_my_status->status_flags & SERVER_MORE_RESULTS_EXISTS) {
                p_my_status->conn_stat = PROTO_CONN_RESPONSE_TABULAR;
            } else {
                p_my_status->conn_stat = PROTO_CONN_REQUEST;
            }
            break;
        case PROTO_CONN_PREPARED_PARAMETERS:
            if (p_my_status->stmt_num_fields > 0) {
                p_my_status->conn_stat = PROTO_CONN_PREPARED_FIELDS;
            } else {
                p_my_status->conn_stat = PROTO_CONN_REQUEST;
            }
            break;

        case PROTO_CONN_RESPONSE_SHOW_FIELDS:
        case PROTO_CONN_PREPARED_FIELDS:
            p_my_status->conn_stat = PROTO_CONN_REQUEST;
            break;

        case PROTO_CONN_AUTH_SWITCH_REQUEST:
            if (is_auth_switch_request(pkt, len))
            {
                p_my_status->conn_stat = PROTO_CONN_AUTH_SWITCH_RESPONSE;
            }
            else
            {
                p_my_status->conn_stat = PROTO_CONN_REQUEST;
            }
            break;

        case PROTO_CONN_RESPONSE_PREPARE:
            break;

        case PROTO_CONN_RESPONSE_TABULAR:
            break;
        default:
            /* This should be an unreachable case */
            p_my_status->conn_stat = PROTO_CONN_REQUEST;
        }

        break;

    case MYSQL_RESPONSE_PACKET_ERR:
        unpack_err_packet(pkt, len, p_my_status, p_result);
        p_my_status->conn_stat = PROTO_CONN_REQUEST;
        break;

    case MYSQL_RESPONSE_PACKET_DATA:
    {
        proto_conn_status conn_stat = p_my_status->conn_stat;
        if ((p_my_status->conn_stat == PROTO_CONN_AUTH_SWITCH_RESPONSE) && (*pkt == 0x01))
        {
            /* Extra Authentication Data, prefixed with 0x01 */
            conn_stat = PROTO_CONN_AUTH_SWITCH_REQUEST;
        }

        if (conn_stat == PROTO_CONN_FIELD_PACKET && p_my_status->num_fields == p_result->col_cnt)
        {
            p_my_status->conn_stat = conn_stat = PROTO_CONN_ROW_PACKET;
        }

        switch (conn_stat) {
        case PROTO_CONN_RESPONSE_MESSAGE:
            p_my_status->conn_stat = PROTO_CONN_REQUEST;
            break;

        case PROTO_CONN_RESPONSE_TABULAR:
            if ((uint8_t)(*pkt) == 0xfb)
            {
                ret = unpack_local_infile_request(pkt, len, p_my_status, p_result);
            }
            else
            {
                ret = unpack_result_set_header(pkt, len, p_my_status, p_result);
            }
            break;

        case PROTO_CONN_FIELD_PACKET:
        case PROTO_CONN_RESPONSE_SHOW_FIELDS:
        case PROTO_CONN_RESPONSE_PREPARE:
        case PROTO_CONN_PREPARED_PARAMETERS:
            ret = unpack_field_packet(pkt, len, p_my_status, p_result);
            break;

        case PROTO_CONN_ROW_PACKET:
            ret = unpack_row_packet(pkt, len, p_result);
            break;

        case PROTO_CONN_PREPARED_FIELDS:
            ret = unpack_field_packet(pkt, len, p_my_status, p_result);
            break;

        default:
            // mysql_set_conn_state(pinfo, conn_data, UNDEFINED);
            ret = PARSER_STATUS_CONTINUE;
        }
    }
        break;
    }

    return ret;
}

bool CMysqlParser::packet_on_right_status(int dir, mysql_status_t *p_status)
{
    if (STREAM_REQ == dir)
    {
        if (p_status->conn_stat == PROTO_CONN_INIT ||
            p_status->conn_stat == PROTO_CONN_AUTH_SWITCH_REQUEST)
        {
            return false;
        }
    }
    else if (p_status->conn_stat == PROTO_CONN_LOGIN ||
            p_status->conn_stat == PROTO_CONN_REQUEST)
    {
        return false;
    }
    return true;
}

/**
 * Deal with the packet payload.
 */
int CMysqlParser::parse_mysql_packet(const char *pkt,
                                    size_t len,
                                    int dir,
                                    mysql_status_t *p_my_status,
                                    mysql_parsed_data_t *p_result)
{
    int ret = PARSER_STATUS_FINISH;
    int resp_pkt_type;

    if (!packet_on_right_status(dir, p_my_status))
    {
        return PARSER_STATUS_CONTINUE;
    }

    if (dir == STREAM_RSP)
    {
        /**
          如果状态没改变，tcp stream的offset应该保持不变，等待下一次状态的改变
        */
        if (PROTO_CONN_INIT == p_my_status->conn_stat)
        {
            // init handshake, unpack payload
            ret = unpack_server_greeting(pkt, len, p_my_status);
            p_my_status->conn_stat = PROTO_CONN_LOGIN;
        }
        else
        {
            ret = parse_mysql_response(pkt, len, p_my_status, p_result);
        }
    }
    else
    {
        if (PROTO_CONN_LOGIN == p_my_status->conn_stat)
        {
            // handshake response
            ret = unpack_login_request(pkt, len, p_my_status, p_result);
            if ((p_my_status->server_cap_flags & CLIENT_PLUGIN_AUTH) && (p_my_status->client_cap_flags & CLIENT_PLUGIN_AUTH))
            {
                // change to auth switch
                p_my_status->conn_stat = PROTO_CONN_AUTH_SWITCH_REQUEST;
            }
            else if ((p_my_status->server_cap_flags & CLIENT_SSL) && (p_my_status->client_cap_flags & CLIENT_SSL))
            {
                // ssl connection, skip ...
                ret = PARSER_STATUS_DROP_DATA;
            }
            else
            {
                p_my_status->conn_stat = PROTO_CONN_RESPONSE_OK;
            }
        }
        else
        {
            ret = parse_mysql_request(pkt, len, p_my_status, p_result);
        }
    }

    return ret;
}

const char *CMysqlParser::to_str_charset(int id)
{
    switch(id)
    {
        case 1: return "big5";
        case 3: return "dec8";
        case 4: return "cp850";
        case 6: return "hp8";
        case 7: return "koi8r";
        case 8: return "latin1";
        case 9: return "latin2";
        case 10: return "swe7";
        case 11: return "ascii";
        case 12: return "ujis";
        case 13: return "sjis";
        case 16: return "hebrew";
        case 18: return "tis620";
        case 19: return "euckr";
        case 22: return "koi8u";
        case 24: return "gb2312";
        case 25: return "greek";
        case 26: return "cp1250";
        case 28: return "gbk";
        case 30: return "latin5";
        case 32: return "armscii8";
        case 33: return "utf8";
        case 35: return "ucs2";
        case 36: return "cp866";
        case 37: return "keybcs2";
        case 38: return "macce";
        case 39: return "macroman";
        case 40: return "cp852";
        case 41: return "latin7";
        case 51: return "cp1251";
        case 54: return "utf16";
        case 56: return "utf16le";
        case 57: return "cp1256";
        case 59: return "cp1257";
        case 60: return "utf32";
        case 63: return "binary";
        case 92: return "geostd8";
        case 95: return "cp932";
        case 97: return "eucjpms";
        case 248: return "gb18030";
        case 255: return "utf8mb4";
    }
    return "";
}

void CMysqlParser::free_row_data(mysql_row_data_t *p_row, int col_cnt, bool in_session)
{
    if (p_row && p_row->row)
    {
        int i;
        b_string_t **pp_row = p_row->row;
        for (i = 0; i < col_cnt; i++)
        {
            if (in_session)
            {
                // 释放分配的b_string_t内存
                BSTR_SAFE_FREE((*(pp_row + i))->s);
            }
            free(*(pp_row + i));
        }
        free(pp_row);
    }
}

void CMysqlParser::del_mysql_parsed_data(mysql_parsed_data_t *p_data, bool in_session)
{
    if (p_data)
    {
        int i;
        int col_cnt = p_data->col_cnt;
        column_def_t *p;
        while (p_data->col_def)
        {
            p = p_data->col_def;
            p_data->col_def = p_data->col_def->next;

            if (in_session)
            {
                // 释放分配的b_string_t内存
                BSTR_SAFE_FREE(p->schema.s);
                BSTR_SAFE_FREE(p->table.s);
                BSTR_SAFE_FREE(p->column.s);
                BSTR_SAFE_FREE(p->default_val.s);
            }
            free(p);
        }

        if (p_data->rs_rows)
        {
            mysql_row_data_t **rs_rows = p_data->rs_rows;
            for (i = 0; i < p_data->row_cnt; i++)
            {
                mysql_row_data_t *row = *(rs_rows + i);
                free_row_data(row, col_cnt, in_session);
                free(row);
            }
            free(rs_rows);
        }

        if (in_session)
        {
            BSTR_SAFE_FREE(p_data->sql.s);
        }

        if (p_data->err_msg)
        {
            free(p_data->err_msg);
        }
    }
}

mysql_half_stream_t *CMysqlParser::create_session_mysql_parser()
{
    mysql_half_stream_t *p_mhs = (mysql_half_stream_t *)malloc(sizeof(mysql_half_stream_t));

    if (NULL != p_mhs)
    {
        p_mhs->next = NULL;
        memset(&p_mhs->data, 0, sizeof(mysql_parsed_data_t));
    }

    return p_mhs;
}

void CMysqlParser::del_mysql_status(mysql_stream_t *p_stream)
{
    BSTR_SAFE_FREE(p_stream->my_stat.user.s);
    BSTR_SAFE_FREE(p_stream->my_stat.password.s);
    BSTR_SAFE_FREE(p_stream->my_stat.db_name.s);
    BSTR_SAFE_FREE(p_stream->my_stat.server_version.s);
}

/**
 * 压缩算法是deflate，zlib中的lz4，将正常情况下的完整packet压缩后放在 Compressed Packet Header后。
 * 压缩数据包格式：
 * +--------------+----------+-----------------+---------+
 * | 3B 压缩后长度 | 1B seqId | 3B 未压缩时的长度 | payload |
 * +--------------+----------+-----------------+---------+
 */
int CMysqlParser::uncompress_mysql_packet(char **pkt, size_t len, char **uncomp, size_t *uncomp_len)
{
    const char *orig_pkt = *pkt;
    size_t &tmp_len = *uncomp_len;
    tmp_len = read_mysql_packet_length(orig_pkt + 4);

    orig_pkt += 7;
    if (tmp_len == 0)
    {
        // payload 过小，未压缩
        *pkt = (char *)orig_pkt;
        tmp_len = len;
        return PARSER_STATUS_FINISH;
    }

    // 解压缩payload
    char *uncomp_buf = (char *)malloc(tmp_len);
    if (uncompress((unsigned char*)uncomp_buf, &tmp_len, (const unsigned char*)orig_pkt, len) != Z_OK)
    {
        GWLOG_WARN(m_comm, "%s uncompress one mysql packet failed!\n", MYSQL_LOG_PRE);
        return PARSER_STATUS_DROP_DATA;
    }
    *uncomp = uncomp_buf;

    return PARSER_STATUS_FINISH;
}

void CMysqlParser::print_mem(const void * mem, size_t size)
{
    if (NULL == mem)
    {
        printf("%s memory data is null\n", MYSQL_LOG_PRE);
        return;
    }

    unsigned char * v = (unsigned char *)(mem);
    char buf[69] = {' '};
    buf[68] = '\0';
    int i, idx = 0, idx_ch = 51, cnt = 0;
    for (i = 0; i < size; i++)
    {
        ++cnt;
        unsigned char ch = *(v + i);

        sprintf(buf + idx, "%02X ", ch);
        sprintf(buf + idx_ch, "%c", ch >= 32 ? ch : '.');
        idx += 3;
        ++idx_ch;
        if (cnt == 8)
        {
            sprintf(buf+idx, " ");
            sprintf(buf+idx_ch, " ");
            ++idx_ch;
            ++idx;
        }
        else if (cnt == 16)
        {
            sprintf(buf+idx, " ");
            buf[idx+1] = ' ';
            idx = 0;
            idx_ch = 51;
            cnt = 0;
            printf("%s\n", buf);
            memset(buf, ' ', sizeof(buf));
            buf[68] = '\0';
        } else if ((i + 1) == size)
        {
            buf[idx] = ' ';
            printf("%s\n", buf);
        }
    }
}



