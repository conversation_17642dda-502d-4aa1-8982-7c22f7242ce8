/*
* @Author: <PERSON><PERSON>
* @Date:   2019-01-07 19:17:34
* @Last Modified by:   <PERSON><PERSON>
* @Last Modified time: 2019-01-08 16:42:57
*/

#include "mysql_parser.h"
#include "mysql_parser_common.h"

#include "gw_common.h"
#include "gw_logger.h"

/**
 * 增加上层协议解析对象。
 * @param CParser *parser
 */
void CMysqlParser::add_upstream(CParser *parser)
{
}

/**
 * 清空上层协议解析对象
 */
void CMysqlParser::reset_upstream(void)
{
}

/**
 * 推送到上层消息(异步方式, Json序列化数据)
 * @param char *s
 * @param size_t *length
 */
void CMysqlParser::push_upstream_msg(char *s, size_t length)
{
  GWLOG_TEST(m_comm, "hive test s=%p length=%u\n", s, length);
}