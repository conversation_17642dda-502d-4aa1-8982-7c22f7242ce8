#!/bin/sh


DEST_PATH=/opt/apigw/gwhw/
DPDK_ALLOC_GB_FILE="/opt/apigw/gwhw/tools/dpdk_alloc_GB"
DPDK_ALLOC_GB=$(expr \( $(cat /proc/meminfo |grep 'MemTotal' |awk -F : '{print $2}' |sed 's/^[ \t]*//g' | cut -f1 -d\ ) / 1048576 + 1 \) / 2)

function get_dpdk_alloc_gb()
{
    if [[ -f $DPDK_ALLOC_GB_FILE ]];then
        DPDK_ALLOC_GB=`cat ${DPDK_ALLOC_GB_FILE}`
    fi
}

function bind()
{
    if [[ "$(lsmod | grep "^vfio-pci" | wc -l )" -gt 0 ]]; then
        echo already mount vfio-pci
        exit 0
    fi

    # kernel_check

    sudo modprobe vfio enable_unsafe_noiommu_mode=1;sudo modprobe vfio-pci

    if [[ ! -d /mnt/huge ]]; then
        sudo mkdir -p /mnt/huge
    fi

    # 最多不超过物理内存的50%
    if [[ -d /sys/devices/system/node/node0/hugepages/hugepages-1048576kB/ ]]; then
        #sudo su -c "echo 8 > /sys/devices/system/node/node0/hugepages/hugepages-1048576kB/nr_hugepages"
        size=$(expr \( $(cat /proc/meminfo |grep 'MemTotal' |awk -F : '{print $2}' |sed 's/^[ \t]*//g' | cut -f1 -d\ ) / 1048576 + 1 \) / 2)
        if [[ $DPDK_ALLOC_GB -gt $size ]];then
            alloc_size=$size
        else
            alloc_size=$DPDK_ALLOC_GB
        fi
        echo $alloc_size
        #sudo su -c "echo $(expr \( $(cat /proc/meminfo |grep 'MemTotal' |awk -F : '{print $2}' |sed 's/^[ \t]*//g' | cut -f1 -d\ ) / 1048576 + 1 \) / 2) > /sys/devices/system/node/node0/hugepages/hugepages-1048576kB/nr_hugepages"
        sudo su -c "echo $alloc_size > /sys/devices/system/node/node0/hugepages/hugepages-1048576kB/nr_hugepages"
    fi
    if [[ -d /sys/devices/system/node/node0/hugepages/hugepages-2048kB/ ]]; then
        if [[ `cat /sys/devices/system/node/node0/hugepages/hugepages-1048576kB/nr_hugepages 2>/dev/null || echo 0` -eq 0 ]]; then
            #sudo su -c "echo 4096 > /sys/devices/system/node/node0/hugepages/hugepages-2048kB/nr_hugepages"
            sudo su -c "echo $(expr \( $(cat /proc/meminfo |grep 'MemTotal' |awk -F : '{print $2}' |sed 's/^[ \t]*//g' | cut -f1 -d\ ) / 1048576 + 1 \) \* 1024 / 2 / 2) > /sys/devices/system/node/node0/hugepages/hugepages-2048kB/nr_hugepages"
        fi
    fi

    if [[ -d /sys/devices/system/node/node1/hugepages/hugepages-1048576kB/ ]]; then
        sudo su -c "echo 2 > /sys/devices/system/node/node1/hugepages/hugepages-1048576kB/nr_hugepages"
    fi
    if [[ -d /sys/devices/system/node/node1/hugepages/hugepages-2048kB/ ]]; then
        if [[ `cat /sys/devices/system/node/node1/hugepages/hugepages-1048576kB/nr_hugepages 2>/dev/null || echo 0` -eq 0 ]]; then
            sudo su -c "echo 1024 > /sys/devices/system/node/node1/hugepages/hugepages-2048kB/nr_hugepages"
        fi
    fi

    for i in $(seq 2 8); do
        if [[ -d /sys/devices/system/node/node${i}/hugepages/hugepages-1048576kB/ ]]; then
            sudo su -c "echo 2 > /sys/devices/system/node/node${i}/hugepages/hugepages-1048576kB/nr_hugepages"
        fi
        if [[ -d /sys/devices/system/node/node${i}/hugepages/hugepages-2048kB/ ]]; then
            if [[ `cat /sys/devices/system/node/node${i}/hugepages/hugepages-1048576kB/nr_hugepages 2>/dev/null || echo 0` -eq 0 ]]; then
                sudo su -c "echo 1024 > /sys/devices/system/node/node${i}/hugepages/hugepages-2048kB/nr_hugepages"
            fi
        fi
    done


    if [[ "`mount | grep "/mnt/huge" | wc -l`" -gt 0 ]]; then
        #sudo supervisorctl -c "${DEST_PATH}./supervisord_hw.conf"   stop all
        sudo systemctl stop gwhw
        sudo umount /mnt/huge
        sudo rm -rf /mnt/huge
        sudo mkdir -p /mnt/huge
    fi

    if [[ -d /sys/devices/system/node/node0/hugepages/hugepages-1048576kB/ ]] && [[ `cat /sys/devices/system/node/node0/hugepages/hugepages-1048576kB/nr_hugepages 2>/dev/null || echo 0` -ne 0 ]]; then
        sudo mount -t hugetlbfs nodev /mnt/huge -o pagesize=1GB
    else
        sudo mount -t hugetlbfs nodev /mnt/huge -o pagesize=2MB
    fi
}

if [[ $# -eq 0 ]];then
    get_dpdk_alloc_gb
    bind
fi
