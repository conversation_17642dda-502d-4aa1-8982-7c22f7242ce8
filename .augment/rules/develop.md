---
type: "always_apply"
---

# 网关项目开发和编译规则

## 1. 版本控制规则

### Git版本管理
- **版本控制系统**: 当前项目使用Git进行版本管理
- **提交要求**: 对代码进行任何修改后，必须进行代码提交
- **提交信息规范**: 提交信息应清晰描述修改内容和目的，格式如下：
  ```
  [模块名] 简短描述

  详细说明：
  - 修改的具体内容
  - 修改的原因和目的
  - 影响范围和注意事项
  ```

### 分支管理
- **主分支**: `main` 或 `master` 分支为稳定版本
- **开发分支**: 功能开发在独立分支进行
- **合并要求**: 代码合并前需要通过编译验证

## 2. 开发和编译环境规则

### 环境配置
- **开发环境**: Mac本地环境 (`/Users/<USER>/gw-hw`)
- **编译环境**: Linux远程服务器容器环境
- **架构支持**: 支持x86和ARM两种架构编译

### 远程编译规则
- **编译验证要求**: 代码修改后需要编译验证时，必须调用gwhw_mcp这个MCP服务进行远程编译
- **编译工具**: 使用gwhw_mcp工具集中的相关API进行编译操作

### gwhw_mcp服务API使用方法

#### 插件编译API
1. **单独插件编译**:
   ```json
   start_build_plugin_api_v1_build_plugin_post_gwhw_mcp({
     "plugin_name": "postgre_parser.so",
     "remote_ip": "",
     "ssh_user": "",
     "container_name": ""
   })
   ```

2. **插件部署**:
   ```json
   start_plugin_deploy_api_v1_deploy_plugin_post_gwhw_mcp({
     "plugin_name": "postgre_parser.so",
     "remote_ip": "",
     "ssh_user": "",
     "container_name": ""
   })
   ```

3. **插件构建部署统一任务** (推荐):
   ```json
   start_build_deploy_plugin_api_v1_build_deploy_plugin_post_gwhw_m({
     "plugin_name": "postgre_parser.so",
     "build_remote_ip": "",
     "build_ssh_user": "",
     "deploy_remote_ip": "",
     "deploy_ssh_user": "",
     "container_name": ""
   })
   ```

#### 任务监控API
- **任务状态查询**: `get_task_api_v1_tasks__task_id__get_gwhw_mcp(task_id)`
- **任务列表查看**: `get_tasks_api_v1_tasks_get_gwhw_mcp({"status": "running", "limit": 10})`

#### 参数说明
- `plugin_name`: 插件名称，如 `postgre_parser.so`、`http_parser.so`
- `remote_ip`: 部署服务器IP地址（用户需要提供）
- `ssh_user`: SSH用户名（可选，优先从凭据存储获取）
- `container_name`: 容器名称（可选，使用默认值）

## 3. 协议解析器插件开发规则

### 插件化架构
- **架构模式**: 当前协议解析代码采用插件化架构
- **插件接口**: 所有解析器插件继承自 `CParser` 基类
- **动态加载**: 插件通过动态库(.so文件)形式加载

### 现有插件参考
主要的现有插件作为参考示例：
- **HTTP解析器**: `src/hw/gw_parser/parser/http_parser/` - 支持HTTP/1.x协议解析
- **HTTP2解析器**: `src/hw/gw_parser/parser/http2_parser/` - 支持HTTP/2协议解析
- **PostgreSQL解析器**: `src/hw/gw_parser/parser/postgre_parser/` - 支持PostgreSQL协议解析
- **gRPC解析器**: `src/hw/gw_parser/parser/grpc_parser/` - 支持gRPC协议解析
- **FTP解析器**: `src/hw/gw_parser/parser/ftp_parser/` - 支持FTP协议解析
- **MongoDB解析器**: `src/hw/gw_parser/parser/mongo_parser/` - 支持MongoDB协议解析
- **SMB解析器**: `src/hw/gw_parser/parser/smb_parser/` - 支持SMB协议解析
- **SSL解析器**: `src/hw/gw_parser/parser/ssl_parser/` - 支持SSL/TLS协议解析

### 插件目录结构和文件命名规范

#### 标准目录结构
```
src/hw/gw_parser/parser/{protocol}_parser/
├── Makefile                                    # 编译配置文件
├── {protocol}_parser.h                         # 主解析器类头文件
├── {protocol}_parser.cpp                       # 主解析器类实现
├── {protocol}_parser_common.h                  # 公共定义和数据结构
├── {protocol}_parser_deal_probe.cpp            # 协议探测逻辑
├── {protocol}_parser_deal_parser.cpp           # 核心解析逻辑
├── {protocol}_parser_parser_msg.cpp            # 消息解析和事件处理
├── module_mgt_{protocol}_parser.cpp            # 模块管理和导出函数
├── {protocol}_parser_upload_task_worker.cpp    # 上传任务工作线程
├── {protocol}_parser_upload_task_worker.hpp    # 上传任务工作线程头文件
├── ProtobufRaw{Protocol}Event.proto           # protobuf事件定义(可选)
├── ProtobufRaw{Protocol}Event.pb.h            # protobuf生成头文件(可选)
├── ProtobufRaw{Protocol}Event.pb.cpp          # protobuf生成源文件(可选)
└── readme.md                                   # 插件说明文档
```

#### 文件命名约定
- **主类文件**: `{protocol}_parser.h` 和 `{protocol}_parser.cpp`
- **公共定义**: `{protocol}_parser_common.h`
- **功能模块**: `{protocol}_parser_{function}.cpp`
- **类名规范**: `C{Protocol}Parser` (如 `CPostgreParser`)
- **插件输出**: `{protocol}_parser.so`

### 插件核心组件

#### 必需文件说明
1. **主解析器类** (`{protocol}_parser.cpp`):
   - 继承自 `CParser` 基类
   - 实现协议探测、解析、上传等核心接口
   - 管理插件生命周期

2. **协议探测逻辑** (`{protocol}_parser_deal_probe.cpp`):
   - 实现 `probe()` 方法识别协议流量
   - 支持连接建立、关闭、重置时的探测

3. **核心解析逻辑** (`{protocol}_parser_deal_parser.cpp`):
   - 实现 `parse()` 方法解析协议数据
   - 处理协议状态机和数据流

4. **消息解析器** (`{protocol}_parser_parser_msg.cpp`):
   - 处理具体的协议消息类型
   - 格式化事件数据(JSON/protobuf)
   - 管理事件上传

5. **模块管理** (`module_mgt_{protocol}_parser.cpp`):
   - 实现插件导出函数
   - 提供插件加载接口

## 4. 代码风格和架构规范

### 编码规范

#### 命名约定
- **变量名**: 使用camelCase命名，如 `httpRequestInfo`
- **函数名**: 使用PascalCase命名，如 `ParseHttpRequest()`
- **类名**: 使用PascalCase，以C开头，如 `CHttpParser`
- **常量**: 使用大写字母和下划线，如 `HTTP_HEADER_MAX_LENGTH`
- **宏定义**: 使用大写字母和下划线，如 `POSTGRE_LOG_PRE`

#### 代码风格
- **缩进**: 使用4个空格或制表符，保持与现有代码一致
- **大括号**: 所有 `if`、`for`、`while` 语句都使用大括号，即使只有一行
- **异常处理**: 异常情况优先处理，正常情况判断在后
- **注释**: 使用中文注释说明复杂逻辑和业务含义

#### 头文件组织
```cpp
// 系统头文件
#include <stdlib.h>
#include <string.h>

// 第三方库头文件
#include <cJSON.h>

// 项目公共头文件
#include "gw_i_parser.h"
#include "gw_common.h"

// 模块内部头文件
#include "{protocol}_parser_common.h"
```

### 架构模式

#### 类设计模式
- **继承关系**: 所有解析器继承自 `CParser` 基类
- **接口实现**: 必须实现 `probe()`、`parse()` 等虚函数
- **资源管理**: 使用RAII模式管理资源，确保异常安全

#### 错误处理方式
- **日志记录**: 使用 `GWLOG_ERROR`、`GWLOG_WARN` 等宏记录错误
- **返回值**: 使用标准返回码，0表示成功，负数表示错误
- **异常安全**: 确保资源正确释放，避免内存泄漏

### 文件组织结构

#### 项目根目录结构
```
src/hw/gw_parser/
├── core/                    # 核心库代码
├── include/                 # 公共头文件
├── parser/                  # 协议解析器插件
├── upload/                  # 数据上传模块
├── source/                  # 数据源模块
├── utils/                   # 工具库
├── test/                    # 测试代码
├── Makefile                 # 主编译文件
└── gw_main.cpp             # 主程序入口
```

#### 编译配置

##### Makefile标准模板
```makefile
# 编译器配置
ifeq ("$(BUILD_CC_TOOL)","clang++")
CC = clang++ -fsanitize=address -fno-omit-frame-pointer -D_CC_CLANG_PP
else ifeq ("$(BUILD_CC_TOOL)","g++")
CC = g++ -D_CC_GNU_PP
else
CC = gcc
endif

# 路径配置
ROOT_DIR := $(MKFILE_DIR_STRIP)/../..
CFLAGS += -fvisibility=hidden -fPIC -I. -I$(ROOT_DIR)/include
CFLAGS += -I$(ROOT_DIR)/utils/cjson/ -I$(ROOT_DIR)/core/

# 架构相关配置
ifeq ("$(BUILD_ARCH)", "ARM")
CFLAGS += -I/home/<USER>/3rd/libpcap-1.9.1/
LDFLAGS += -L/home/<USER>/3rd/zlib/lib/ -lz
else ifeq ("$(BUILD_ARCH)", "x86")
LDFLAGS += -lz -lpthread
endif

# 目标文件
O_FILES = {protocol}_parser.o {protocol}_parser_parser_msg.o
O_FILES += module_mgt_{protocol}_parser.o

# 编译规则
{protocol}_parser.so: $(O_FILES)
	$(CC) -o $@ $^ $(LDFLAGS) $(LIBS) $(LIB)
```

### protobuf集成规范

#### protobuf文件组织
- **定义文件**: `ProtobufRaw{Protocol}Event.proto`
- **生成文件**: `ProtobufRaw{Protocol}Event.pb.h` 和 `ProtobufRaw{Protocol}Event.pb.cpp`
- **编译集成**: 在Makefile中添加protobuf库链接

#### protobuf编译配置
```makefile
# protobuf支持
ifeq ("$(BUILD_ARCH)", "x86")
CFLAGS += -I/usr/include/protobuf
LDFLAGS += -lprotobuf
else ifeq ("$(BUILD_ARCH)", "ARM")
CFLAGS += -I/home/<USER>/3rd/protobuf/include/
LDFLAGS += -L/home/<USER>/3rd/protobuf/lib/ -lprotobuf
endif

# 添加protobuf对象文件
O_FILES += ProtobufRaw{Protocol}Event.pb.o
```

#### protobuf生成命令
```bash
# 生成C++文件
protoc --cpp_out=. ProtobufRaw{Protocol}Event.proto
```

### 依赖管理

#### 第三方库依赖
- **cJSON**: JSON数据处理
- **protobuf**: 二进制数据序列化
- **zlib**: 数据压缩
- **openssl**: 加密和SSL支持

#### 库文件路径
- **x86架构**: 使用系统默认路径或 `/opt/` 下的库
- **ARM架构**: 使用 `/home/<USER>/3rd/` 下的交叉编译库

### 插件编译部署标准流程

#### 开发流程
1. **代码修改**: 在本地Mac环境进行代码开发
2. **代码提交**: 提交代码到Git仓库
3. **远程编译**: 使用gwhw_mcp API进行远程编译验证
4. **错误修复**: 根据编译结果修复代码问题
5. **部署测试**: 编译成功后进行部署测试
6. **功能验证**: 验证插件功能正常工作

#### 编译验证步骤
1. 调用 `start_build_deploy_plugin_api_v1_build_deploy_plugin_post_gwhw_m`
2. 监控任务状态直到完成
3. 检查编译日志和错误信息
4. 修复问题后重新编译
5. 确认编译成功和部署正常

#### 质量保证
- **编译警告**: 处理所有编译警告
- **内存安全**: 使用工具检查内存泄漏
- **代码审查**: 确保代码符合项目规范
- **功能测试**: 验证插件核心功能正常

## 5. 开发注意事项

### 性能考虑
- **内存使用**: 注重内存使用效率，特别是解析过程中的资源分配和释放
- **并发安全**: 考虑多线程环境下的数据安全
- **解析效率**: 优化协议解析性能，避免阻塞主流程

### 兼容性要求
- **向后兼容**: 保持与现有功能的向后兼容性
- **架构一致**: 遵循现有插件的架构模式
- **接口稳定**: 不随意修改公共接口

### 调试和测试
- **日志输出**: 合理使用日志记录关键信息
- **单元测试**: 为核心功能编写单元测试
- **集成测试**: 进行完整的集成测试验证

这些规则为网关项目的开发提供了全面的指导，确保代码质量和项目的可维护性。
